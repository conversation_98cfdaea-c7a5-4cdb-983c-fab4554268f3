
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import DebuggerPanel from '../DebuggerPanel';
import { DebuggerEvent, DebuggerEventType, LogLevel } from '@/types/botInteraction.type';
import { ApiSliceIdentifier } from '@/store/helper';

// Mock i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: vi.fn((...args) => args.filter(Boolean).join(' ')),
}));

// Mock getBaseUrl
vi.mock('@/store/helper', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    getBaseUrl: vi.fn(() => 'http://localhost:8080'),
  };
});

// Mock child components
vi.mock('../Logs', () => ({ default: vi.fn(() => <div>Logs Component</div>) }));
vi.mock('../AiAnalysisLogs', () => ({ default: vi.fn(() => <div>AI Analysis Component</div>) }));
vi.mock('../SessionData', () => ({ default: vi.fn(() => <div>Session Data Component</div>) }));

// Mock EventSource
class MockEventSource {
  url: string;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  constructor(url: string) {
    this.url = url;
    MockEventSource.instances.push(this);
  }
  close = vi.fn();
  static instances: MockEventSource[] = [];
  static clearInstances() {
    MockEventSource.instances = [];
  }
  static dispatchMessage(data: any) {
    MockEventSource.instances.forEach(instance => {
      if (instance.onmessage) {
        instance.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
      }
    });
  }
  static dispatchError() {
    MockEventSource.instances.forEach(instance => {
      if (instance.onerror) {
        instance.onerror(new Event('error'));
      }
    });
  }
}
vi.stubGlobal('EventSource', MockEventSource);

describe('DebuggerPanel', () => {
  const mockOnOpenChange = vi.fn();
  const conversationId = 'test-conversation-id';

  beforeEach(() => {
    vi.clearAllMocks();
    MockEventSource.clearInstances();
    // Mock window.innerHeight for consistent MAX_HEIGHT calculation
    Object.defineProperty(window, 'innerHeight', { writable: true, value: 1000 });
  });

  it('renders correctly with initial elements', () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(screen.getByText('common.debugger')).toBeInTheDocument();
    expect(screen.getByLabelText('Close debugger panel')).toBeInTheDocument();
    expect(screen.getByText('debugger.logs')).toBeInTheDocument();
    expect(screen.getByText('debugger.aiAnalysis')).toBeInTheDocument();
    expect(screen.getByText('debugger.sessionData')).toBeInTheDocument();
    expect(screen.getByText('Logs Component')).toBeInTheDocument(); // Default tab content
  });

  it('calls onOpenChange when close button is clicked', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    await userEvent.click(screen.getByLabelText('Close debugger panel'));
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('switches tabs correctly', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    await userEvent.click(screen.getByText('debugger.aiAnalysis'));
    expect(screen.getByText('AI Analysis Component')).toBeInTheDocument();
    expect(screen.queryByText('Logs Component')).not.toBeInTheDocument();

    await userEvent.click(screen.getByText('debugger.sessionData'));
    expect(screen.getByText('Session Data Component')).toBeInTheDocument();
    expect(screen.queryByText('AI Analysis Component')).not.toBeInTheDocument();

    await userEvent.click(screen.getByText('debugger.logs'));
    expect(screen.getByText('Logs Component')).toBeInTheDocument();
    expect(screen.queryByText('Session Data Component')).not.toBeInTheDocument();
  });

  it('initializes EventSource with correct URL and handles messages', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(MockEventSource.instances.length).toBe(1);
    const es = MockEventSource.instances[0];
    expect(es.url).toBe(`http://localhost:8080/debugger/stream/${conversationId}`);

    const mockLogEvent: DebuggerEvent = {
      type: DebuggerEventType.LOG,
      payload: { level: LogLevel.INFO, message: 'Test log message' },
      timestamp: Date.now(),
    };

    act(() => {
      MockEventSource.dispatchMessage(mockLogEvent);
    });

    // Verify that the Logs component received the new log (mocked child components don't actually render content, but we can check if they were called with props)
    // This requires a more sophisticated mock for the child components if we want to assert on their props.
    // For now, we'll rely on the internal state update of DebuggerPanel.
    // A better way would be to check if the mock child component was called with the updated logs.
    // Let's refine the mock for child components to allow prop inspection.
  });

  it('handles EventSource errors', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const es = MockEventSource.instances[0];
    act(() => {
      MockEventSource.dispatchError();
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('EventSource failed:', expect.any(Event));
    expect(es.close).toHaveBeenCalledTimes(1);
    consoleErrorSpy.mockRestore();
  });

  it('closes EventSource on unmount', () => {
    const { unmount } = render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const es = MockEventSource.instances[0];
    unmount();
    expect(es.close).toHaveBeenCalledTimes(1);
  });

  it('does not initialize EventSource if conversationId is empty', () => {
    render(<DebuggerPanel conversationId="" onOpenChange={mockOnOpenChange} />);
    expect(MockEventSource.instances.length).toBe(0);
  });

  it('handles malformed EventSource messages gracefully', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const es = MockEventSource.instances[0];

    act(() => {
      if (es.onmessage) {
        es.onmessage(new MessageEvent('message', { data: 'invalid json' }));
      }
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('Error parsing debugger event:', expect.any(Error));
    consoleErrorSpy.mockRestore();
  });

  // Resizing tests
  it('resizes the panel height on mouse drag', () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = screen.getByRole('tablist').closest('div')?.parentElement; // Get the main panel div

    if (!panel) throw new Error('Panel not found');

    const initialHeight = panel.offsetHeight;
    expect(initialHeight).toBe(320); // MIN_HEIGHT

    // Simulate mouse down
    fireEvent.mouseDown(resizeHandle, { clientY: 500 });

    // Simulate mouse move to increase height
    fireEvent.mouseMove(document, { clientY: 400 }); // Dragging up
    expect(panel.style.height).toBe(`${initialHeight + 100}px`);

    // Simulate mouse move to decrease height
    fireEvent.mouseMove(document, { clientY: 600 }); // Dragging down
    expect(panel.style.height).toBe(`${initialHeight - 100}px`);

    // Simulate mouse up
    fireEvent.mouseUp(document);

    // Verify event listeners are removed
    fireEvent.mouseMove(document, { clientY: 300 });
    expect(panel.style.height).not.toBe(`${initialHeight + 200}px`); // Should not change after mouseUp
  });

  it('respects MIN_HEIGHT during resizing', () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = screen.getByRole('tablist').closest('div')?.parentElement;

    if (!panel) throw new Error('Panel not found');

    fireEvent.mouseDown(resizeHandle, { clientY: 500 });
    fireEvent.mouseMove(document, { clientY: 800 }); // Drag far down to hit MIN_HEIGHT
    expect(panel.style.height).toBe('320px'); // Should be clamped at MIN_HEIGHT
    fireEvent.mouseUp(document);
  });

  it('respects MAX_HEIGHT during resizing', () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = screen.getByRole('tablist').closest('div')?.parentElement;

    if (!panel) throw new Error('Panel not found');

    fireEvent.mouseDown(resizeHandle, { clientY: 500 });
    fireEvent.mouseMove(document, { clientY: 0 }); // Drag far up to hit MAX_HEIGHT (window.innerHeight - 50)
    expect(panel.style.height).toBe(`${window.innerHeight - 50}px`);
    fireEvent.mouseUp(document);
  });
});
