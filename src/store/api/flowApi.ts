import { apiSlice } from '../apiSlice';
export interface UpdateBotRequest {
  name: string;
  domain: string;
  description: string;
}

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Flow'],
});
// --- Flow Types ---
export interface Flow {
  id: string;
  name: string;
  type: 'Default' | 'Custom';
  nodes: any[];
  connections: any[];
  isActive: boolean;
}

// --- Payload for creating a single flow ---
interface CreateSingleFlowPayload {
  name: string;
  description: string;
  botId: string;
  entryNodeId: string;
  nodes: Record<string, any>;
  metadata: Record<string, any>;
  type: 'Default' | 'Custom';
}

// --- API Slice ---
export const flowApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // GET all flows for a bot
    getFlows: builder.query<Flow[], { botId: string }>({
      query: ({ botId }) => `/bots/${botId}/flows`,
      transformResponse: (response: { data: { items: Flow[] } }) => response.data.items,
      providesTags: ['Flow'],
    }),

    // GET single flow by ID
    getFlowById: builder.query<Flow, { appId: string; flowId: string }>({
      query: ({ flowId }) => `/flows/${flowId}`,
      transformResponse: (response: { data: Flow }) => response.data,
      providesTags: ['Flow'],
    }),

    // POST - Create a new flow
    createFlow: builder.mutation<Flow, { appId: string; payload: CreateSingleFlowPayload }>({
      query: ({ payload }) => {
        return {
          url: `/flows`,
          method: 'POST',
          body: payload,
        };
      },
      transformResponse: (response: { data: { appId: string; flow: Flow } }) => {
        return response.data.flow;
      },
      invalidatesTags: ['Flow'],
    }),

    // PUT - Update an existing flow
    updateFlow: builder.mutation<
      Flow,
      { appId: string; flowId: string; payload: Partial<CreateSingleFlowPayload> }
    >({
      query: ({ flowId, payload }) => {
        return {
          url: `/flows/${flowId}`,
          method: 'PUT',
          body: payload,
        };
      },
      transformResponse: (response: { data: Flow }) => response.data,
      invalidatesTags: ['Flow'],
    }),

    // DELETE - Delete a flow
    deleteFlow: builder.mutation<void, { appId: string; flowId: string }>({
      query: ({ appId, flowId }) => {
        return {
          url: `/flows/${flowId}/apps/${appId}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['Flow'],
    }),

    // POST - Clone a flow
    cloneFlow: builder.mutation<Flow, { flowId: string }>({
      query: ({ flowId }) => ({
        url: `/flows/${flowId}/clone`,
        method: 'POST',
      }),
      transformResponse: (response: { data: Flow }) => response.data,
      invalidatesTags: ['Flow'],
    }),
  }),
});

// --- Export Hooks ---
export const {
  useGetFlowsQuery,
  useGetFlowByIdQuery,
  useCreateFlowMutation,
  useUpdateFlowMutation,
  useDeleteFlowMutation,
  useCloneFlowMutation,
} = flowApiSlice;
