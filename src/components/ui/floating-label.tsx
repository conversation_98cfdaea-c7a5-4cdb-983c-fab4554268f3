import React, { useCallback, useState } from 'react';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from './select';
import { cn } from '@/lib/utils';

interface Option {
  value: string;
  label: string;
  className?: string;
}

interface FloatingFieldProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement> &
      React.TextareaHTMLAttributes<HTMLTextAreaElement> &
      React.SelectHTMLAttributes<HTMLSelectElement>,
    'onChange' | 'value'
  > {
  label: string;
  value?: string;
  onChange?: (value: string) => void;
  as?: 'input' | 'textarea' | 'select';
  options?: Option[];
  type?: string;
  className?: string;
  id?: string;
}

const FloatingField = React.memo(
  React.forwardRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement, FloatingFieldProps>(
    (
      {
        label,
        value = '',
        onChange,
        as = 'input',
        options = [],
        type = 'text',
        className,
        id,
        disabled,
        ...props
      },
      ref
    ) => {
      const [isFocused, setIsFocused] = useState(false);
      const hasValue = value?.toString().length > 0;

      const handleFocus = useCallback(() => setIsFocused(true), []);
      const handleBlur = useCallback(() => setIsFocused(false), []);
      const handleChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string) => {
          const newValue = typeof e === 'string' ? e : e.target.value;
          if (!id)
            onChange?.(e); //TODO: need to rm this, this is only here for backward compatibility
          else onChange?.(newValue);
        },
        [onChange]
      );

      const sharedProps = {
        value,
        onFocus: handleFocus,
        onBlur: handleBlur,
        disabled,
        id,
        'aria-labelledby': `${id}-label`,
        className: cn(
          'w-full appearance-none rounded-md border border-tertiary-300 bg-background px-4 py-3 text-base text-tertiary-900 transition-colors duration-200 disabled:opacity-50 dark:bg-tertiary-800 dark:text-background dark:border-tertiary-600',
          className
        ),
        placeholder: ' ',
        ...props,
      };

      const labelClasses = cn(
        'absolute left-4 top-1/2 -translate-y-1/2 bg-background px-1 text-tertiary-500 transition-all duration-200 pointer-events-none dark:bg-tertiary-800',
        isFocused || hasValue
          ? '-top-0 text-xs font-normal'
          : 'text-base font-normal'
      );

      const componentMap = {
        input: (
          <input
            {...sharedProps}
            ref={ref as React.Ref<HTMLInputElement>}
            type={type}
            onChange={handleChange}
          />
        ),
        textarea: (
          <textarea
            {...sharedProps}
            ref={ref as React.Ref<HTMLTextAreaElement>}
            onChange={handleChange}
            rows={3}
          />
        ),
        select: (
          <Select value={value} onValueChange={handleChange} disabled={disabled}>
            <SelectTrigger className={sharedProps.className} aria-labelledby={`${id}-label`}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent aria-labelledby={`${id}-select`}>
              {options.map(opt => (
                <SelectItem key={opt.value} value={opt.value} className={opt.className}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ),
      };

      return (
        <div className="relative w-full">
          {componentMap[as]}
          <label htmlFor={id} id={`${id}-label`} className={labelClasses}>
            {label}
          </label>
        </div>
      );
    }
  )
);

FloatingField.displayName = 'FloatingField';

export { FloatingField, type FloatingFieldProps, type Option };
