import z from 'zod';
import { FormField } from './types/types';
import { format } from 'date-fns';
import { PhoneNumberUtil, PhoneNumberFormat } from 'google-libphonenumber';

const phoneUtil = PhoneNumberUtil.getInstance();

export const createPhoneSchema = (field: FormField) =>
  z
    .string()
    .optional()
    .refine(
      val => {
        if (!val || val.trim() === '') return !field.required;

        try {
          const regionCode = phoneUtil.getRegionCodeForNumber(phoneUtil.parse(val));

          const parsed = phoneUtil.parseAndKeepRawInput(val, regionCode);

          return phoneUtil.isValidNumberForRegion(parsed, regionCode);
        } catch (err) {
          console.warn('Phone validation failed', err);
          return false;
        }
      },
      { message: `${field.label || field.fieldName} is not a valid phone number` }
    );

const isValidDate = (value: string | Date): boolean => {
  const date = value instanceof Date ? value : new Date(value);
  return !isNaN(date.getTime()) && date.toString() !== 'Invalid Date';
};

const withRequired = <T extends z.ZodTypeAny>(schema: T, field: FormField, message: string) =>
  field.required
    ? schema.refine(val => val !== undefined && val !== '', { message })
    : schema.optional();

export const createTextFieldSchema = (field: FormField) =>
  withRequired(
    z
      .string()
      .trim()
      .min(1, { message: `${field.label || field.fieldName} is required` }),
    field,
    `${field.label || field.fieldName} is required`
  );

export const createEmailSchema = (field: FormField) =>
  withRequired(
    z.string().email({ message: `${field.label || field.fieldName} is invalid` }),
    field,
    `${field.label || field.fieldName} is required`
  );

export const createPasswordSchema = (field: FormField) =>
  withRequired(
    z
      .string()
      .min(6, { message: `${field.label || field.fieldName} must be at least 6 characters` }),
    field,
    `${field.label || field.fieldName} is required`
  );

export const createTimeSchema = (field: FormField) => {
  const timeRegex24 = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  const timeRegex12 = /^(1[0-2]|0?[1-9]):[0-5][0-9]\s?(AM|PM|am|pm)?$/;
  const isValidTime = (val: string) => timeRegex24.test(val) || timeRegex12.test(val);

  return withRequired(
    z
      .string()
      .refine(val => (!field.required ? val === '' || isValidTime(val) : isValidTime(val)), {
        message: `${field.label || field.fieldName} must be a valid time (e.g., HH:MM or HH:MM AM/PM)`,
      }),
    field,
    `${field.label || field.fieldName} is required`
  );
};

export const createPastDateSchema = (field: FormField) => {
  const currentDate = new Date('2025-08-03T11:37:00Z');

  return withRequired(
    z
      .union([z.string(), z.date()])
      .refine(isValidDate, { message: `${field.label || field.fieldName} must be a valid date` })
      .transform(val => (typeof val === 'string' ? new Date(val) : val))
      .refine(val => !val || val < currentDate, {
        message: `${field.label || field.fieldName} must be a past date (before ${format(
          currentDate,
          'PPP p'
        )})`,
      }),
    field,
    `${field.label || field.fieldName} is required`
  );
};

export const createFutureDateSchema = (field: FormField) => {
  const currentDate = new Date('2025-08-03T11:37:00Z');

  return withRequired(
    z
      .union([z.string(), z.date()])
      .refine(isValidDate, { message: `${field.label || field.fieldName} must be a valid date` })
      .transform(val => (typeof val === 'string' ? new Date(val) : val))
      .refine(val => !val || val > currentDate, {
        message: `${field.label || field.fieldName} must be a Future date (before ${format(
          currentDate,
          'PPP p'
        )})`,
      }),
    field,
    `${field.label || field.fieldName} is required`
  );
};
// export const createCustomDateSchema = (field: FormField) => {
//   const rangeStart = field.rangeStart
//     ? isValidDate(field.rangeStart)
//       ? new Date(field.rangeStart)
//       : new Date(0)
//     : new Date(0);
//   const rangeEnd = field.rangeEnd
//     ? isValidDate(field.rangeEnd)
//       ? new Date(field.rangeEnd)
//       : new Date('2025-08-03T11:37:00Z')
//     : new Date('2025-08-03T11:37:00Z');

//   return withRequired(
//     z
//       .object({
//         start: z
//           .union([z.string(), z.date()])
//           .optional()
//           .transform(val => (typeof val === 'string' ? new Date(val) : val))
//           .refine(val => !val || isValidDate(val), {
//             message: `${field.label || field.fieldName}.start must be a valid date`,
//           }),
//         end: z
//           .union([z.string(), z.date()])
//           .optional()
//           .transform(val => (typeof val === 'string' ? new Date(val) : val))
//           .refine(val => !val || isValidDate(val), {
//             message: `${field.label || field.fieldName}.end must be a valid date`,
//           }),
//       })
//       .superRefine((data, ctx) => {
//         const { start, end } = data;

//         if (field.required && (!start || !end)) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} is required`,
//           });
//           return;
//         }

//         if (!start || !end) return;

//         if (isNaN(start.getTime())) {
//           ctx.addIssue({
//             path: ['start'],
//             code: 'custom',
//             message: `${field.label || field.fieldName}.start must be a valid date`,
//           });
//         }

//         if (isNaN(end.getTime())) {
//           ctx.addIssue({
//             path: ['end'],
//             code: 'custom',
//             message: `${field.label || field.fieldName}.end must be a valid date`,
//           });
//         }

//         if (start > end) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} end date must be after start date`,
//           });
//         }

//         if (start < rangeStart || end > rangeEnd) {
//           ctx.addIssue({
//             code: 'custom',
//             message: `${field.label || field.fieldName} must be between ${format(
//               rangeStart,
//               'PPP'
//             )} and ${format(rangeEnd, 'PPP')}`,
//           });
//         }
//       }),
//     field,
//     `${field.label || field.fieldName} is required`
//   );
// };

export const createNumberSchema = (field: FormField) => {
  const base = z
    .string()
    .refine(val => val === '' || !isNaN(Number(val)), {
      message: `${field.label || field.fieldName} must be a valid number`,
    })
    .transform(val => (val === '' ? undefined : Number(val)))
    .refine(val => val === undefined || typeof val === 'number', {
      message: `${field.label || field.fieldName} must be a valid number`,
    })
    .refine(val => val === undefined || !isNaN(val), {
      message: `${field.label || field.fieldName} must be a valid number`,
    });

  return withRequired(base, field, `${field.label || field.fieldName} is required`).optional();
};

export const createFormValidationSchema = (fields: FormField[]) =>
  z.object(
    fields.reduce(
      (schema, field) => {
        switch (field.fieldType) {
          case 'text':
          case 'text_field':
            schema[field.fieldName] = createTextFieldSchema(field);
            break;
          case 'email':
            schema[field.fieldName] = createEmailSchema(field);
            break;
          case 'password':
            schema[field.fieldName] = createPasswordSchema(field);
            break;
          case 'number':
            schema[field.fieldName] = createNumberSchema(field);
            break;
          case 'time':
            schema[field.fieldName] = createTimeSchema(field);
            break;
          case 'past_date':
            schema[field.fieldName] = createPastDateSchema(field);
            break;
          case 'future_date':
            schema[field.fieldName] = createFutureDateSchema(field);
            break;
          //   case 'custom_date':
          //     schema[field.fieldName] = createCustomDateSchema(field);
          //     break;
          case 'mobile_number':
            schema[field.fieldName] = createPhoneSchema(field);
            break;

          default:
            schema[field.fieldName] = z.string().optional();
        }
        return schema;
      },
      {} as Record<string, z.ZodTypeAny>
    )
  );
