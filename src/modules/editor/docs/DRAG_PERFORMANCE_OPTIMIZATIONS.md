# Stencil Drag Performance Optimizations

## Problem
When picking up a node and moving the cursor, the node would stay stuck at the initial drag position for a few seconds before jumping to the cursor and working correctly. This caused a poor user experience with laggy and unresponsive drag operations.

## Root Causes Identified

1. **Heavy synchronous operations during drag initialization**
   - `getStencilByType()` creates new JointJS element definitions on every drag
   - `getModuleIcon()` and `getModuleText()` perform lookups
   - Complex cell creation with attributes and ports

2. **Blocking main thread operations**
   - All drag initialization happened synchronously
   - No use of `requestAnimationFrame` for smooth operations

3. **Missing visual feedback**
   - No immediate visual response to user interaction
   - No optimized CSS for drag operations

4. **Suboptimal stencil configuration**
   - Default drag settings not optimized for performance

## Solutions Implemented

### 1. Cell Template Pre-creation and Caching
- **File**: `src/modules/editor/hooks/useStencil.ts`
- **Changes**:
  - Added `cellTemplates` ref to cache pre-created node templates
  - Created `createCellTemplate()` function to generate and cache templates
  - Pre-warm common node types (`appStart`, `message`, `choice`, `form`, `appEnd`) on initialization

### 2. Asynchronous Drag Initialization
- **File**: `src/modules/editor/hooks/useStencil.ts`
- **Changes**:
  - Wrapped drag start logic in `requestAnimationFrame()` for smooth execution
  - Use pre-created templates instead of creating new ones on each drag
  - Optimized dependency array in `useCallback`

### 3. Enhanced Visual Feedback
- **File**: `src/modules/editor/widget/Stencil/NodeItem.tsx`
- **Changes**:
  - Added immediate visual feedback with `isDragInitializing` state
  - Implemented pointer capture for better drag tracking
  - Added data attributes for CSS targeting during drag operations
  - Enhanced event handling with proper prevention and propagation control

### 4. CSS Performance Optimizations
- **File**: `src/modules/editor/styles/stencil-optimizations.css`
- **Features**:
  - GPU acceleration with `transform: translateZ(0)` and `will-change`
  - Optimized transitions and hover effects
  - CSS containment for better rendering performance
  - Touch device optimizations
  - High DPI display support
  - Reduced motion support for accessibility

### 5. Stencil Configuration Optimization
- **File**: `src/modules/editor/hooks/useStencil.ts`
- **Changes**:
  - Disabled `dropAnimation` for better performance
  - Disabled `scaleClones` to reduce computation
  - Disabled `usePaperGrid` for smoother drag movement

### 6. Component Structure Improvements
- **File**: `src/modules/editor/widget/Stencil/NodeList.tsx`
- **Changes**:
  - Added optimized CSS classes (`stencil-container`, `stencil-grid`)
  - Improved component structure for better performance

## Performance Benefits

1. **Immediate Response**: Drag operations now start immediately with visual feedback
2. **Smooth Movement**: Using `requestAnimationFrame` ensures smooth drag tracking
3. **Reduced Computation**: Pre-created templates eliminate heavy operations during drag
4. **Better Visual Feedback**: Users see immediate response to their interactions
5. **GPU Acceleration**: CSS optimizations leverage hardware acceleration
6. **Memory Efficiency**: Template caching reduces repeated object creation

## Testing

- **File**: `src/modules/editor/hooks/__tests__/useStencil.test.ts`
- **Coverage**:
  - Stencil initialization with performance options
  - Template pre-creation and caching
  - Asynchronous drag start with `requestAnimationFrame`
  - Event handling optimizations

## Usage

The optimizations are automatically applied when using the stencil component. No additional configuration is required.

## Browser Compatibility

- Modern browsers with `requestAnimationFrame` support
- CSS optimizations include vendor prefixes for broader compatibility
- Graceful degradation for older browsers

## Future Improvements

1. **Web Workers**: Move heavy computations to background threads
2. **Virtual Scrolling**: For large node lists
3. **Intersection Observer**: Lazy load node icons
4. **Service Worker Caching**: Cache node templates and icons
5. **WebGL Acceleration**: For complex drag operations

## Monitoring

Monitor these metrics to ensure continued performance:
- Time from pointer down to drag start
- Frame rate during drag operations
- Memory usage with template caching
- User interaction responsiveness scores
