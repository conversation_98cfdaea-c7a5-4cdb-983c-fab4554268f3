import { ApiResponse, OrderArray, OrderDirection, PaginatedResponse } from '@/types';
import { TagDescription, SerializeQueryArgs } from '@reduxjs/toolkit/query';

export type FlexibleApiInstance = {
  util: { updateQueryData: any; prefetch: any };
  endpoints: { [key: string]: { select: (args: any) => (state: any) => any } };
};

export interface OptimizedTagConfig<T> {
  entityType: T;
}

interface EntityConfig<
  Entity,
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg,
> {
  entityType: TagType;
  apiInstance: ApiInstance;
  getSerializeQueryArgs: SerializeQueryArgs<any>;
  paginationMerge?: <T>(
    currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
    newItems: ApiResponse<PaginatedResponse<T>>,
    meta: { arg: any }
  ) => ApiResponse<PaginatedResponse<T>>;
  options?: {
    listEndpointName?: string;
    itemEndpointName?: string;
    itemPrefetch?: boolean;
  };
  shouldUpdateCache?: (queryArgs: QueryArg, initialArgs: QueryArg, newEntity: Entity) => boolean;
  getItemQueryArgs?: (entity: Entity) => QueryArg;
}

export const createOptimizedTags = <T extends string>(config: OptimizedTagConfig<T>) => {
  const { entityType } = config;

  const createListTag = (): TagDescription<T> => ({ type: entityType, id: 'LIST' });
  const createEntityTag = (id: string): TagDescription<T> => ({ type: entityType, id });

  return {
    providesList: (): TagDescription<T>[] => [createListTag()],
    providesItem: (_result: unknown, _error: unknown, arg: { id: string }): TagDescription<T>[] => [
      createEntityTag(arg.id),
    ],
    invalidatesItem: (id: string): TagDescription<T>[] => [createEntityTag(id)],
    invalidatesList: (): TagDescription<T>[] => [createListTag()],
  };
};

const multiCompare = <T extends Record<string, any>>(a: T, b: T, orders: OrderArray): number => {
  for (const [field, dir] of orders) {
    const va = a[field];
    const vb = b[field];
    let cmp = 0;
    if (va == null && vb == null) continue;
    if (va == null) cmp = 1;
    else if (vb == null) cmp = -1;
    else if (typeof va === 'string' && typeof vb === 'string') cmp = va.localeCompare(vb);
    else cmp = va < vb ? -1 : va > vb ? 1 : 0;
    if (cmp !== 0) return dir === OrderDirection.ASC ? cmp : -cmp;
  }
  return 0;
};

const getOrderBy = <QueryArg extends Record<string, any>>(cacheKey: QueryArg) => {
  const order = cacheKey?.order || cacheKey?.query?.order;
  return order;
};

export const createCacheUpdaters = <T extends { id: string }>() => ({
  updateItemInLists: (
    draft: ApiResponse<PaginatedResponse<T>>,
    updatedItem: T,
    orderBy?: OrderArray
  ) => {
    if (draft?.data?.items) {
      const index = draft.data.items.findIndex(item => item.id === updatedItem.id);
      if (index !== -1) {
        // Remove the old item
        draft.data.items.splice(index, 1);

        const insertIndex = orderBy?.length
          ? draft.data.items.findIndex(item => multiCompare(item, updatedItem, orderBy) > 0)
          : index;

        draft.data.items.splice(
          insertIndex === -1 ? draft.data.items.length : insertIndex,
          0,
          updatedItem
        );
      }
    }
  },
  removeItemFromLists: (draft: ApiResponse<PaginatedResponse<T>>, itemId: string) => {
    if (draft?.data?.items) {
      draft.data.items = draft.data.items.filter(item => item.id !== itemId);
      if (draft.data.pagination) {
        draft.data.pagination.total = Math.max(0, draft.data.pagination.total - 1);
      }
    }
  },
  addItemToLists: (draft: ApiResponse<PaginatedResponse<T>>, newItem: T, orderBy?: OrderArray) => {
    if (draft?.data?.items) {
      // Check if item already exists to prevent duplicates
      const existingIndex = draft.data.items.findIndex(item => item.id === newItem.id);
      if (existingIndex !== -1) {
        draft.data.items[existingIndex] = newItem;
        return;
      }
      const insertIndex = orderBy?.length
        ? draft.data.items.findIndex(item => multiCompare(item, newItem, orderBy) > 0)
        : 0;
      draft.data.items.splice(
        insertIndex === -1 ? draft.data.items.length : insertIndex,
        0,
        newItem
      );
      if (draft.data.pagination) draft.data.pagination.total += 1;
    }
  },
});

export const defaultSerializeListArgs: SerializeQueryArgs<any> = ({ queryArgs, endpointName }) => {
  return `${endpointName}-${JSON.stringify(queryArgs)}`;
};

export const createInfiniteScrollHelpers = () => ({
  mergePaginatedData: <T>(
    existing: ApiResponse<PaginatedResponse<T>> | undefined,
    incoming: ApiResponse<PaginatedResponse<T>>
  ): ApiResponse<PaginatedResponse<T>> => {
    if (!existing?.data) return incoming;

    // Prevent duplicate items by filtering out existing IDs.
    // This relies on `item.id` being a unique identifier for each item. If duplicates persist, ensure `item.id` is truly unique and consistent.
    const existingIds = new Set((existing.data.items || []).map((item: any) => item.id));
    const newItems = (incoming.data?.items || []).filter((item: any) => !existingIds.has(item.id));

    return {
      ...incoming,
      data: {
        ...incoming.data,
        items: [...(existing.data.items || []), ...newItems],
        pagination: incoming.data!.pagination,
      },
    };
  },
  hasMorePages: (data: ApiResponse<PaginatedResponse<unknown>> | undefined): boolean => {
    return data?.data?.pagination?.hasNext || false;
  },
  getNextPage: (data: ApiResponse<PaginatedResponse<unknown>> | undefined): number => {
    const currentPage = data?.data?.pagination?.page || 1;
    return currentPage + 1;
  },
});

export const defaultPaginatedMerge = <T, QueryArg>(
  currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
  newItems: ApiResponse<PaginatedResponse<T>>,
  _meta: { arg: QueryArg }
): ApiResponse<PaginatedResponse<T>> => {
  return createInfiniteScrollHelpers().mergePaginatedData(currentCache, newItems);
};

export const defaultForceRefetch = <QueryArg>({
  currentArg,
  previousArg,
}: {
  currentArg?: QueryArg;
  previousArg?: QueryArg;
}) => {
  const current = currentArg as any;
  const previous = previousArg as any;

  return JSON.stringify(current || {}) !== JSON.stringify(previous || {});
};

export const createEntityMixins = <
  Entity extends { id: string },
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg extends Record<string, any>,
>(
  config: EntityConfig<Entity, TagType, ApiInstance, QueryArg>
) => {
  const { entityType, getSerializeQueryArgs, paginationMerge } = config;

  const tags = createOptimizedTags<TagType>({ entityType });

  const paginatedMixin = getPaginatedListQueryMixin<TagType, QueryArg>({
    tags,
    options: {
      serializeQueryArgs: getSerializeQueryArgs,
      merge: paginationMerge || defaultPaginatedMerge,
      forceRefetch: defaultForceRefetch,
    },
  });

  const itemMixin = getItemQueryMixin<TagType>({ tags });

  const { create, update, remove } = getMutationMixins(config);
  return {
    paginated: paginatedMixin,
    item: itemMixin,
    create,
    update,
    delete: remove,
    tags,
  };
};

export const getPaginatedListQueryMixin = <TagType extends string, QueryArg>(config: {
  tags: ReturnType<typeof createOptimizedTags<TagType>>;
  options?: {
    serializeQueryArgs?: SerializeQueryArgs<QueryArg>;
    merge?: <T>(
      currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
      newItems: ApiResponse<PaginatedResponse<T>>,
      meta: { arg: QueryArg }
    ) => ApiResponse<PaginatedResponse<T>>;
    forceRefetch?: (args: { currentArg?: QueryArg; previousArg?: QueryArg }) => boolean;
  };
}) => {
  const { tags, options = {} } = config;
  const {
    serializeQueryArgs = defaultSerializeListArgs,
    merge = defaultPaginatedMerge,
    forceRefetch = defaultForceRefetch,
  } = options;

  return {
    providesTags: tags.providesList,
    serializeQueryArgs,
    merge,
    forceRefetch,
  };
};

export const getItemQueryMixin = <TagType extends string>(config: {
  tags: ReturnType<typeof createOptimizedTags<TagType>>;
}) => {
  const { tags } = config;
  return {
    providesTags: tags.providesItem,
  };
};

export const getMutationMixins = <
  Entity extends { id: string },
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg extends Record<string, any>, // Make QueryArg more flexible
>(
  config: EntityConfig<Entity, TagType, ApiInstance, QueryArg>
) => {
  const { entityType, shouldUpdateCache, apiInstance, getItemQueryArgs, options } = config;

  const apiUtil = apiInstance.util;

  const tags = createOptimizedTags<TagType>({ entityType });
  const updaters = createCacheUpdaters<Entity>();

  const { listEndpointName = `get${entityType}s`, itemEndpointName = `get${entityType}` } =
    options || {};

  const create = {
    async onQueryStarted(arg: QueryArg, api: any) {
      const { dispatch, queryFulfilled, getState } = api;
      try {
        const { data: newEntityData } = await queryFulfilled;
        if (newEntityData.success && newEntityData.data) {
          const state = getState();
          const apiState = state.api;
          if (!newEntityData.data) {
            throw new Error('Mutation response data is undefined or null.');
          }

          const updatedEntityData: Entity = newEntityData.data;

          if (options?.itemPrefetch) {
            const itemQueryArgs = getItemQueryArgs?.(updatedEntityData) ?? {
              id: updatedEntityData.id,
            };
            await dispatch(apiUtil.prefetch(itemEndpointName, itemQueryArgs, { force: true }));

            apiInstance.endpoints[itemEndpointName].select(
              config.getSerializeQueryArgs({
                queryArgs: itemQueryArgs,
                endpointName: itemEndpointName,
              })
            )(getState());
          }

          Object.keys(apiState.queries).forEach(key => {
            const query = apiState.queries[key];
            if (query?.endpointName === listEndpointName) {
              const cacheKeyArgs = query.originalArgs;

              const shouldProceed =
                shouldUpdateCache?.(cacheKeyArgs, arg, updatedEntityData) ?? true;

              if (shouldProceed) {
                dispatch(
                  apiUtil.updateQueryData(
                    listEndpointName,
                    cacheKeyArgs,
                    (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                      const orderBy = getOrderBy(cacheKeyArgs);
                      updaters.addItemToLists(draft, updatedEntityData, orderBy);
                    }
                  )
                );
              }
            }
          });
        }
      } catch {
        console.log('Error in onQueryStarted. Undoing patchResult.');
      }
    },
  };

  const update = {
    invalidatesTags: (_result: unknown, _error: unknown, { id }: { id: string }) =>
      tags.invalidatesItem(id),

    async onQueryStarted(arg: QueryArg & { id: string }, api: any) {
      const { dispatch, queryFulfilled, getState } = api;
      const { id } = arg;

      const patchResult = dispatch(
        apiUtil.updateQueryData(itemEndpointName, { id }, (draft: ApiResponse<Entity>) => {
          if (draft.data) {
            Object.assign(draft.data, arg);
          }
        })
      );

      try {
        const { data: updatedEntityResponse } = await queryFulfilled;
        if (updatedEntityResponse.success && updatedEntityResponse.data) {
          const updatedEntityData: Entity = updatedEntityResponse.data;

          if (options?.itemPrefetch) {
            const itemQueryArgs = getItemQueryArgs?.(updatedEntityData) ?? {
              id: updatedEntityData.id,
            };
            await dispatch(apiUtil.prefetch(itemEndpointName, itemQueryArgs, { force: true }));

            apiInstance.endpoints[itemEndpointName].select(
              config.getSerializeQueryArgs({
                queryArgs: itemQueryArgs,
                endpointName: itemEndpointName,
              })
            )(getState())?.data;
          }

          if (updatedEntityData) {
            const state = getState();
            const apiState = state.api;

            Object.keys(apiState.queries).forEach(key => {
              const query = apiState.queries[key];
              if (query?.endpointName === listEndpointName) {
                const cacheKeyArgs = query.originalArgs;

                dispatch(
                  apiUtil.updateQueryData(
                    listEndpointName,
                    cacheKeyArgs,
                    (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                      const orderBy = getOrderBy(cacheKeyArgs);
                      updaters.updateItemInLists(draft, updatedEntityData, orderBy);
                    }
                  )
                );
              }
            });
          }
        }
      } catch (error) {
        console.log('Error in onQueryStarted (update). Undoing patchResult.', error);
        (patchResult as { undo: () => void }).undo();
      }
    },
  };

  const remove = {
    invalidatesTags: (_result: unknown, _error: unknown, { id }: { id: string }) =>
      tags.invalidatesItem(id),

    async onQueryStarted(arg: { id: string }, api: any) {
      const { dispatch, queryFulfilled, getState } = api;
      const { id } = arg;
      const patchResults: Array<{ undo: () => void }> = [];

      try {
        const state = getState();
        const api = state.api;

        Object.keys(api.queries).forEach(key => {
          const query = api.queries[key];
          if (query?.endpointName === listEndpointName) {
            const cacheKey = query.originalArgs;

            const patchResult = dispatch(
              apiUtil.updateQueryData(
                listEndpointName,
                cacheKey,
                (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                  updaters.removeItemFromLists(draft, id);
                }
              )
            );
            patchResults.push(patchResult as { undo: () => void });
          }
        });

        await queryFulfilled;
      } catch {
        patchResults.forEach(result => result.undo());
      }
    },
  };

  return {
    create,
    update,
    remove,
  };
};
