import React, { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form';
import { FloatingField } from '@/components/ui/floating-label';
import { DomainOption, TranslationFunction } from '@/types';
import { FileUpload } from '@/components/file-upload';
import { ImageUp } from 'lucide-react';

interface EditFormProps {
  DOMAIN_OPTIONS: DomainOption[];
  t: TranslationFunction;
}

const EditForm: FC<EditFormProps> = ({ DOMAIN_OPTIONS, t }) => {
  const { control } = useFormContext();

  return (
    <div className="space-y-6">
      {/* Bot Name */}
      <FormField
        control={control}
        name="name"
        render={({ field }) => (
          <FormItem className="flex flex-col items-end">
            <FormControl>
              <FloatingField label={t('editor.chatbotName')} type="text" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Domain */}
      <FormField
        control={control}
        name="domain"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                label={t('editor.domain')}
                as="select"
                options={DOMAIN_OPTIONS}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Description */}
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex flex-col items-end">
            <FormControl>
              <FloatingField label={t('editor.description')} as="textarea" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="image"
        disabled
        render={({ field }) => (
          <FormItem className="flex flex-col items-end">
            <FormControl>
              <FileUpload
                uploadLabel={t('editor.uploadImage')}
                maxSize={t('editor.uploadFormat')}
                uploadIcon={<ImageUp />}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default EditForm;
