'use client';
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (confirm: boolean) => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, onOpenChange, onConfirm }) => {
  const { t } = useTranslation();
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-80">
        <DialogHeader>
          <DialogTitle>{t('preview.confirmDialog')}</DialogTitle>
          <DialogDescription>{t('preview.confirmDialogDesc')}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variantColor="success"
            className="border-none rounded-full px-6 py-2"
            onClick={() => onConfirm(true)}
          >
            {t('common.yes')}
          </Button>
          <Button
            variantColor="error"
            className="border-none rounded-full px-6 py-2"
            onClick={() => onConfirm(false)}
          >
            {t('common.no')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmDialog;
