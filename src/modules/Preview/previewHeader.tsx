'use client';

import React, { memo } from 'react';
import { X, ExternalLink, BugOff } from 'lucide-react';
import { PlatformType } from './types/enums';
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

interface PreviewHeaderProps {
  platform: PlatformType;
  setPlatform: (value: PlatformType) => void;
  onDebuggerToggle: () => void;
  onClose: () => void;
}

const PreviewHeader: React.FC<PreviewHeaderProps> = memo(
  ({ platform, setPlatform, onDebuggerToggle, onClose }) => {
    const { t } = useTranslation();
    return (
      <div className="p-4 border-b border-secondary-200">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm text-tertiary-600">{t('common.preview')}</h3>
          <div className="flex items-center space-x-2">
            <div className="relative min-w-32">
              <Select value={platform} onValueChange={setPlatform}>
                <SelectTrigger className="px-4 py-1.5 border-none rounded-full text-sm bg-secondary-50 min-w-24 shadow-none focus:ring-0 focus:outline-none flex items-center justify-center gap-1 h-auto">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(PlatformType).map(value => (
                    <SelectItem key={value} value={value}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button className="!px-1" variant="ghost" variantColor="tertiary">
              <ExternalLink className="w-4 h-4" />
            </Button>
            <Button
              className="!px-1"
              variant="ghost"
              variantColor="tertiary"
              onClick={onDebuggerToggle}
            >
              <BugOff className="w-4 h-4" />
            </Button>
            <Button className="!px-1" variant="ghost" variantColor="tertiary" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

export default PreviewHeader;
