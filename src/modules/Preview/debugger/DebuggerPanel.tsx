import React, { useEffect, useState, useRef } from 'react';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { DebuggerEvent } from '@/types/botInteraction.type';
import { getBaseUrl, ApiSliceIdentifier } from '@/store/helper';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { debuggerTabsConfig } from './config';

interface DebuggerPanelProps {
  conversationId: string;
  onOpenChange: (open: boolean) => void;
}
const MIN_HEIGHT = 320;
const DebuggerPanel = ({ conversationId, onOpenChange }: DebuggerPanelProps) => {
  const { t } = useTranslation();
  const [logs, setLogs] = useState<DebuggerEvent[]>([]);
  const [height, setHeight] = useState(MIN_HEIGHT);
  const panelRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState('logs');

  const MAX_HEIGHT = window.innerHeight - 50;

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    const startY = e.clientY;
    const startHeight = panelRef.current?.offsetHeight || height;

    const handleMouseMove = (e: MouseEvent) => {
      const newHeight = startHeight - (e.clientY - startY);
      setHeight(Math.min(Math.max(newHeight, MIN_HEIGHT), MAX_HEIGHT));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  useEffect(() => {
    if (!conversationId) return;

    const baseUrl = getBaseUrl(ApiSliceIdentifier.BOT_INTERACTION_SERVICE);
    const eventSource = new EventSource(`${baseUrl}/debugger/stream/${conversationId}`);

    eventSource.onmessage = event => {
      try {
        const newEvent: DebuggerEvent = JSON.parse(event.data);
        setLogs(prevLogs => [...prevLogs, newEvent]);
      } catch (error) {
        console.error('Error parsing debugger event:', error);
      }
    };

    eventSource.onerror = error => {
      console.error('EventSource failed:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [conversationId]);

  return (
    <div
      ref={panelRef}
      className="fixed bottom-0 left-0 right-96 h-80 z-50 bg-background border-t border-r border-secondary-200 flex flex-col "
      style={{ height: `${height}px` }}
    >
      <div
        className="absolute top-0 left-0 right-0 h-6 flex items-center justify-center cursor-ns-resize z-10 group"
        onMouseDown={handleMouseDown}
        aria-label="debugger-resize-handle"
      >
        <div className="w-12 h-1 bg-secondary-300 rounded-full group-hover:bg-secondary-400 transition-colors duration-200" />
      </div>

      <div className="flex items-center justify-between font-normal text-base border-b border-secondary-200 p-4">
        <h3>{t('common.debugger')}</h3>
        <button
          onClick={() => onOpenChange(false)}
          className="w-6 h-6 text-secondary-700 hover:text-secondary-900 flex items-center justify-center"
          aria-label="Close debugger panel"
        >
          <X />
        </button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full px-4 flex flex-col flex-1 h-0"
      >
        <TabsList className="flex space-x-6 mt-3 justify-start p-0">
          {debuggerTabsConfig.map(tab => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className={cn('text-sm rounded-br-none rounded-bl-none pb-1 pt-0', {
                'text-primary-600 border-b-2 border-primary-600': activeTab === tab.id,
                'text-secondary-500': activeTab !== tab.id,
              })}
            >
              {t(tab.labelKey)}
            </TabsTrigger>
          ))}
        </TabsList>
        {debuggerTabsConfig.map(tab => (
          <TabsContent key={tab.id} value={tab.id} className="flex-1 overflow-y-auto h-0 pb-5">
            {tab.Component ? (
              <tab.Component logs={logs} />
            ) : (
              <div className="flex items-center justify-center p-6 bg-muted h-full text-secondary-500">
                <p>{t('tabs.contentComingSoon', { tabName: tab.labelKey })}</p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default DebuggerPanel;
