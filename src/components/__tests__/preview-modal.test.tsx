import { render, screen, fireEvent, waitFor } from '@/test/utils';
import PreviewModal from '../preview-modal';
import { vi } from 'vitest';
import userEvent from '@testing-library/user-event';

vi.mock('react-redux', async () => {
  const actual = await vi.importActual('react-redux');
  return {
    ...actual,
    useDispatch: vi.fn(() => vi.fn()),
    useSelector: vi.fn(() => ({
      messages: [],
      showPreview: true,
      isFullScreen: false,
      channel: 'web',
    })),
  };
});

describe('PreviewModal', () => {
  it('renders the modal when showPreview is true', () => {
    render(<PreviewModal />);
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('does not render the modal when showPreview is false', () => {
    vi.mock('react-redux', async () => {
      const actual = await vi.importActual('react-redux');
      return {
        ...actual,
        useDispatch: vi.fn(() => vi.fn()),
        useSelector: vi.fn(() => ({
          messages: [],
          showPreview: false,
          isFullScreen: false,
          channel: 'web',
        })),
      };
    });
    render(<PreviewModal />);
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('displays the input and send button', () => {
    render(<PreviewModal />);
    expect(screen.getByLabelText('Type your message')).toBeInTheDocument();
    expect(screen.getByLabelText('Send')).toBeInTheDocument();
  });

  it('disables send button when input is empty', () => {
    render(<PreviewModal />);
    const sendButton = screen.getByLabelText('Send');
    expect(sendButton).toBeDisabled();
  });

  it('calls dispatch when Debugger button is clicked', async () => {
    const mockDispatch = vi.fn();
    vi.mock('react-redux', async () => {
      const actual = await vi.importActual('react-redux');
      return {
        ...actual,
        useDispatch: vi.fn(() => mockDispatch),
        useSelector: vi.fn(() => ({
          messages: [],
          showPreview: true,
          isFullScreen: false,
          channel: 'web',
        })),
      };
    });
    render(<PreviewModal />);
    const debuggerButton = screen.getByRole('button', { name: 'Debugger' });
    await userEvent.click(debuggerButton);
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('shows confirmation dialog when closing with messages', async () => {
    vi.mock('react-redux', async () => {
      const actual = await vi.importActual('react-redux');
      return {
        ...actual,
        useDispatch: vi.fn(() => vi.fn()),
        useSelector: vi.fn(() => ({
          messages: ['Hello'],
          showPreview: true,
          isFullScreen: false,
          channel: 'web',
        })),
      };
    });
    render(<PreviewModal />);
    const closeButton = screen.getByRole('button', { name: 'Close' });
    fireEvent.click(closeButton);

    const confirmText = await screen.findByText((text) =>
      text.includes('Want to end this conversation?')
    );
    expect(confirmText).toBeInTheDocument();
  });
});