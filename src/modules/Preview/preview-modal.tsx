'use client';
import React, { useState, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import PreviewHeader from './previewHeader';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ConfirmDialog from './ConfirmDialog';
import { PlatformType } from './types/enums';
import { USER_ID, CHANNEL } from './types/constants';
import { useSendPreviewMessageMutation, useResetConversationMutation } from '@/store/api';
import { format } from 'date-fns';
import { BotFormFields, ChatMessage } from './types/types';
import DebuggerPanel from './debugger';
import { SendMessageRequest } from '@/types/botInteraction.type';

const PreviewModal: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { showPreview } = useAppSelector(state => state.ui);
  const [showDebugger, setShowDebugger] = useState(false);
  const { botId } = useBotIdParam();
  const [platform, setPlatform] = useState<PlatformType>(PlatformType.Web);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sendPreviewMessageApi, { isLoading: isSendingMessage }] = useSendPreviewMessageMutation();
  const [resetConversationApi, { isLoading: isResettingConversation }] =
    useResetConversationMutation();
  const [error, setError] = useState<string | null>(null);
  const [lastFormPrompt, setLastFormPrompt] = useState<ChatMessage | null>(null);
  const [lastFormFieldValues, setLastFormFieldValues] = useState<BotFormFields | undefined>(
    undefined
  );
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formLocked, setFormLocked] = useState(false);
  const [isFormActive, setIsFormActive] = useState(false);
  const conversationIdRef = useRef<string>(crypto.randomUUID());

  const isLoading = isSendingMessage || isResettingConversation;

  const chatForm = useForm<{ message: string }>({
    defaultValues: { message: '' },
  });
  const { setValue } = chatForm;

  const isFormValid = useCallback(() => {
    if (!lastFormPrompt) return false;
    return lastFormPrompt.data.prompt.every(
      (field: any) =>
        !field.required || (lastFormFieldValues && lastFormFieldValues[field.fieldName])
    );
  }, [lastFormPrompt, lastFormFieldValues]);

  const sendMessageToServer = useCallback(
    async (body: SendMessageRequest) => {
      setError(null);
      try {
        const data = await sendPreviewMessageApi({
          id: conversationIdRef.current,
          body: {
            ...body,
          },
        }).unwrap();

        const botMessages: ChatMessage[] = [];
        data.data.response.forEach((msg: any) => {
          if (msg.nodeType === 'form') {
            const label = msg.data?.prompt?.[0]?.label;

            if (label) {
              botMessages.push({
                sender: 'bot',
                nodeType: 'message',
                data: {
                  text: label,
                  type: 'text',
                  timestamp: new Date().toISOString(),
                },
              });
            }

            botMessages.push({
              sender: 'bot',
              nodeType: 'form',
              data: msg.data,
            });
          } else if (msg.nodeType === 'message') {
            botMessages.push({
              sender: 'bot',
              nodeType: 'message',
              data: {
                text: msg.data?.text || '',
                type: msg.data?.type || 'text',
                timestamp: msg.data?.timestamp || new Date().toISOString(),
              },
            });
          }
        });

        setMessages(prev => [...prev, ...botMessages]);

        const formMessage = botMessages.find(m => m.nodeType === 'form' && m.data?.prompt?.length);
        if (formMessage) {
          setLastFormPrompt(formMessage);
          const initial: BotFormFields = {};
          formMessage.data.prompt.forEach((field: any) => (initial[field.fieldName] = ''));
          setLastFormFieldValues(initial);
        } else {
          setLastFormPrompt(null);
          setLastFormFieldValues(undefined);
        }
      } catch (err: any) {
        setError(err.message || t('errors.somethingWrong'));
      } finally {
        setFormLocked(false);
      }
    },
    [botId, t, sendPreviewMessageApi]
  );

  const handleChatInputSubmit = useCallback(
    async (fields: { message: string }) => {
      if (isSendingMessage) return;

      const userInput = fields.message.trim();
      if (!userInput) return;

      const isSingleTextFieldForm =
        lastFormPrompt?.data?.prompt?.length === 1 &&
        !lastFormPrompt.data.prompt[0].fieldType.includes('date');

      if (lastFormPrompt && isSingleTextFieldForm) {
        const fieldName = lastFormPrompt.data.prompt[0].fieldName;

        setMessages(prev => [
          ...prev,
          { sender: 'user', nodeType: 'user_message', data: { text: userInput } },
        ]);

        const formPayload = {
          content: JSON.stringify({ [fieldName]: userInput }),
          messageType: 'text',
          formData: { [fieldName]: userInput },
          botId,
          metadata: {
            userId: USER_ID,
            channel: CHANNEL,
            formId: lastFormPrompt.data.formId,
          },
        };

        await sendMessageToServer(formPayload);

        chatForm.reset({ message: '' });
        setValue('message', '');
        return;
      }

      if (lastFormPrompt && (lastFormPrompt.data?.prompt?.length ?? 0) > 0) {
        return; // multi-field form → block sending
      }

      setMessages(prev => [
        ...prev,
        { sender: 'user', nodeType: 'user_message', data: { text: userInput } },
      ]);

      await sendMessageToServer({
        content: userInput,
        messageType: 'text',
        botId,
        metadata: { userId: USER_ID, channel: CHANNEL, conversationId: conversationIdRef.current },
      });

      chatForm.reset({ message: '' });
      setValue('message', '');
    },
    [botId, isSendingMessage, sendMessageToServer, chatForm, lastFormPrompt]
  );

  const handleFormPromptSubmit = useCallback(
    async (fields: BotFormFields) => {
      if (!lastFormPrompt || isSendingMessage) return;

      const prompt = lastFormPrompt.data.prompt as any[];
      const filteredData = Object.fromEntries(
        Object.entries(fields).map(([key, value]) => {
          if (typeof value === 'string') return [key, value];
          if (value instanceof Date) return [key, format(value, 'yyyy-MM-dd')];
          return [key, value ? String(value) : ''];
        })
      );
      setMessages(prev =>
        prev.map(msg =>
          msg === lastFormPrompt
            ? {
                ...msg,
                data: {
                  ...msg.data,
                  submittedValues: filteredData,
                },
              }
            : msg
        )
      );

      setLastFormFieldValues(fields);
      setFormLocked(true);

      await sendMessageToServer({
        content: JSON.stringify(filteredData),
        messageType: 'text',
        formData: filteredData,
        botId,
        metadata: {
          userId: USER_ID,
          channel: CHANNEL,
          formId: lastFormPrompt.data.formId,
          conversationId: conversationIdRef.current,
        },
      });
    },
    [lastFormPrompt, isSendingMessage, sendMessageToServer, botId]
  );

  const resetConversation = useCallback(async () => {
    try {
      await resetConversationApi({ conversationId: conversationIdRef.current }).unwrap();
    } catch (err) {
      console.error('Reset failed:', err);
    }
  }, [resetConversationApi]);

  const clearState = useCallback(() => {
    setMessages([]);
    setValue('message', '');
    setLastFormPrompt(null);
    setLastFormFieldValues(undefined);
    setFormLocked(false);
    setError(null);
    conversationIdRef.current = crypto.randomUUID();
    dispatch(togglePreview());
  }, [dispatch]);

  const handleClose = useCallback(() => {
    if (messages.length > 0) setShowConfirmDialog(true);
    else clearState();
  }, [messages, clearState]);

  const confirmClose = useCallback(
    async (confirm: boolean) => {
      setShowConfirmDialog(false);
      if (confirm) {
        await resetConversation();
        clearState();
      }
    },
    [resetConversation, clearState]
  );

  const isSingleTextField =
    lastFormPrompt &&
    lastFormPrompt.data.prompt?.length === 1 &&
    !lastFormPrompt.data.prompt[0]?.fieldType.includes('date') &&
    (lastFormPrompt.data.formType === 'single-ask-form' ||
      lastFormPrompt.data.formType === 'multi-ask-form');

  if (!showPreview) return null;

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        <PreviewHeader
          platform={platform}
          setPlatform={setPlatform}
          onDebuggerToggle={() => setShowDebugger(!showDebugger)}
          onClose={handleClose}
        />
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col">
            <ChatMessages
              messages={messages}
              lastFormPrompt={lastFormPrompt}
              formLocked={formLocked}
              onFormSubmit={handleFormPromptSubmit}
              lastFormFieldValues={lastFormFieldValues}
              loading={isLoading}
              onFormActiveChange={setIsFormActive}
            />
            {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
            <ChatInput
              activeForm={lastFormPrompt}
              loading={isLoading}
              isFormValid={isFormValid}
              disabled={isFormActive}
              isSingleTextField={!!isSingleTextField}
              onSubmit={handleChatInputSubmit}
              form={chatForm}
            />
          </div>
        </div>
        <ConfirmDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={confirmClose}
        />
      </div>
      {showDebugger && (
        <DebuggerPanel conversationId={conversationIdRef.current} onOpenChange={setShowDebugger} />
      )}
    </div>
  );
};

export default PreviewModal;
