{"common": {"search": "Buscar", "filter": "Filtrar", "create": "CREAR", "save": "GUARDAR", "submit": "<PERSON><PERSON><PERSON>", "cancel": "CANCELAR", "delete": "Eliminar", "add": "AGREGAR", "clone": "Clonar", "export": "Exportar", "edit": "<PERSON><PERSON>", "yes": "SÍ", "no": "NO", "selectOption": "Seleccionar opción", "getStarted": "COMENZAR", "preview": "Vista previa", "publish": "PUBLICAR", "duplicate": "Duplicar", "versionHistory": "Historial de versiones", "flows": "<PERSON><PERSON><PERSON>", "debugger": "Depurador", "message": "Men<PERSON><PERSON>", "image": "Imagen", "file": "Archivo", "video": "Video", "addViaUrl": "Agregar mediante URL", "enterFileUrl": "Introducir URL del archivo", "maxSize": "<PERSON><PERSON><PERSON> máximo: {{size}} MB", "clickOrDrag": "Haz clic o arrastra archivo {{type}} aquí", "clickOrDragFiles": "Haga clic o arrastre el archivo a esta área para cargarlo", "writeMessage": "Escribe un mensaje", "typeMessage": "Escribe tu mensaje...", "fillAboveField": "Completa el formulario arriba para continuar", "dateRange": "Selecciona un rango de fechas", "trackOrder": "Rast<PERSON>r mi pedido", "cancelOrder": "Cancelar mi pedido", "chatWithAgent": "Chatear con un agente", "viewSimilarProducts": "Ver productos similares", "hello": "¡<PERSON><PERSON>, {{name}}!", "howCanIHelp": "¿En qué puedo ayudarte hoy?", "searchFlows": "Buscar flujos...", "onboarding": "Incorporación", "notFound": "No encontrado", "enterValidValue": "Por favor ingresa un valor válido", "translateTo": "Traducir a", "translate": "TRADUCIR", "nothingToShow": "Nada que mostrar", "generate": "Generar", "close": "<PERSON><PERSON><PERSON>", "nodeId": "ID del nodo:", "noData": "Sin datos", "searchEllipsis": "Buscar...", "justNow": "justo ahora", "update": "Actualizar", "error": "Error", "somethingWrong": "Algo salió mal", "saveChanges": "GUARDAR CAMBIOS"}, "debugger": {"logs": "Registros", "aiAnalysis": "Análisis de IA", "sessionData": "Datos de Sesión", "aiAnalysisContent": "Contenido de Análisis de IA próximamente.", "sessionDataContent": "Contenido de Datos de Sesión próximamente.", "noAiAnalysisLogs": "No hay registros de análisis de IA disponibles.", "noSessionDataLogs": "No hay registros de datos de sesión disponibles.", "noLogs": "No hay registros disponibles."}, "preview": {"confirmDialog": "¿Quieres terminar esta conversación?", "confirmDialogDesc": "Esto borrará el chat y cerrará la ventana"}, "validation": {"maxLength": "Este campo no puede superar los {{count}} caracteres."}, "home": {"title": "NeuraTalk AI", "description": "es una solución de IA conversacional de vanguardia diseñada para mejorar el compromiso del cliente, automatizar el soporte y optimizar las operaciones empresariales.", "noResults": "No se encontraron chatbots que coincidan con tu búsqueda.", "lastUpdated": "Última actualización {{date}}"}, "chatbot": {"untitled": "Chatbot sin título", "noDomain": "PREDETERMINADO", "noDescription": "Sin descripción", "confirmDelete": "CONFIRMAR ELIMINACIÓN", "deleteMessage": "¿Estás seguro de que deseas eliminar este chatbot?", "noCancel": "NO, CANCELAR", "yesDelete": "SÍ, ELIMINAR"}, "editor": {"chatbotName": "Nombre del chatbot", "domain": "<PERSON>inio", "description": "Descripción", "uploadImage": "<PERSON>z clic o arrastra un archivo a esta área para subirlo", "uploadFormat": "(Tamaño: hasta 2 MB | Formato: jpg, png)", "unsupportedFile": "Tipo de archivo no compatible", "fileTooLarge": "El archivo debe ser de menos de 2 MB", "invalidName": "Solo se <PERSON>en letras, <PERSON><PERSON><PERSON><PERSON>, gui<PERSON> (-), guiones bajos (_) y puntos (.)", "invalidImageFile": "Por favor sube un archivo de imagen válido (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "El nombre del chatbot es obligatorio", "nameMaxError": "El nombre del chatbot no puede exceder 50 caracteres", "domainRequired": "El dominio es obligatorio", "descMaxError": "La descripción no puede exceder 150 caracteres", "updatedSuccess": "Bot actualizado correctamente", "descRequired": "Se requiere descripción"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "<PERSON><PERSON><PERSON>"}, "domains": {"ecomm": "Comercio electrónico", "telecom": "Telecomunicaciones", "retail": "Retail", "travel": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "emptyState": {"title": "Aún no hay nada aquí", "description": "Actualmente no hay contenido para mostrar."}, "intents": {"title": "Intenciones", "addTitle": "AGREGAR INTENCIÓN", "editTitle": "EDITAR INTENCIÓN", "name": "Nombre de la intención", "namePlaceholder": "Nombre de la intención", "nameLabel": "Nombre de la intención", "nameRequired": "El nombre de la intención es obligatorio.", "startAdding": "Comienza agregando intenciones", "noFlowsConnected": "No hay flujos conectados", "selectToManage": "Selecciona una intención para gestionar expresiones", "loading": "Cargando intenciones.", "loadingError": "Error al cargar intenciones.", "intentAdded": "Intención agregada correctamente.", "intentUpdated": "Intención actualizada correctamente.", "intentDeleted": "Intención eliminada correctamente.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE INTENCIÓN", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar esta intención?", "utterances": {"title": "Expresiones", "addTitle": "AGREGAR EXPRESIÓN", "editTitle": "EDITAR EXPRESIÓN", "enterPlaceholder": "Ingresa una expresión", "startAdding": "Comienza agregando expresiones", "emptyError": "La expresión no puede estar vacía.", "loading": "Cargando expresiones.", "loadingError": "Error al cargar expresiones.", "utteranceAdded": "Expresión agregada.", "utteranceUpdated": "Expresión actualizada.", "utteranceDeleted": "Expresión eliminada.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE EXPRESIÓN", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar esta expresión?"}}, "entities": {"title": "Entidades", "addTitle": "AGREGAR ENTIDAD", "entityName": "Nombre de la entidad", "entityNamePlaceholder": "Nombre de la entidad", "type": "Tipo", "selectType": "Tipo", "enablePartialMatch": "Habilitar coincidencia parcial", "startAdding": "Comienza agregando entidades", "loading": "Cargando entidades...", "error": "Error al cargar entidades.", "types": {"text": "Texto", "list": "Lista", "regex": "REGEX"}, "table": {"name": "Nombre", "type": "Tipo", "value": "Valor", "action": "Acción"}, "validation": {"nameRequired": "El nombre de la entidad es obligatorio.", "typeRequired": "El tipo de entidad es obligatorio.", "valueRequired": "El valor es obligatorio."}, "addValue": "Agregar valor", "editTitle": "EDITAR ENTIDAD", "regexValuePlaceholder": "Valor regex", "entityAdded": "Entidad agregada correctamente.", "entityUpdated": "Entidad actualizada correctamente.", "entityDeleted": "Entidad eliminada correctamente.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE ENTIDAD", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar esta entidad?"}, "train": {"entities": {"title": "Entidades", "content": "Contenido de entidades", "addTitle": "AGREGAR ENTIDAD", "nameLabel": "Nombre de entidad", "intentIdLabel": "ID de intención", "metadataLabel": "Metadatos (JSON)", "metadataPlaceholder": "Ingresa metadatos en formato JSON", "loading": "Cargando entidades...", "error": "Error al cargar entidades.", "validation": {"nameRequired": "El nombre de la entidad es obligatorio.", "intentIdRequired": "El ID de intención es obligatorio.", "invalidJson": "Formato JSON inválido para metadatos."}}, "synonyms": {"title": "Sinónimos", "content": "Contenido de sinónimos"}, "smallTalk": {"title": "Pequeña charla", "content": "Contenido de pequeña charla"}, "trainFromLogs": {"title": "Entrenar desde registros", "content": "Contenido de entrenar desde registros"}, "tabs": {"intentUtterances": "Expresiones de intención", "entities": "Entidades", "faqs": "Preguntas frecuentes", "synonyms": "Sinónimos", "smallTalk": "Pequeña charla", "trainFromLogs": "Entrenar desde registros"}}, "faqs": {"title": "Preguntas y respuestas", "category": {"title": "Categoría", "addTitle": "AGREGAR CATEGORÍA", "editTitle": "EDITAR CATEGORÍA", "nameLabel": "Nombre de la categoría", "nameRequired": "El nombre de la categoría es obligatorio.", "startAdding": "Comienza agregando categorías", "selectToManage": "Selecciona una categoría para gestionar preguntas", "categoryAdded": "Categoría agregada correctamente.", "categoryUpdated": "Categoría actualizada correctamente.", "categoryDeleted": "Categoría eliminada correctamente.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE CATEGORÍA", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar esta categoría?"}, "loading": "Cargando preguntas frecuentes.", "loadingError": "Error al cargar preguntas frecuentes.", "items": {"loading": "Cargando elementos de FAQ...", "loadingError": "Error al cargar elementos de FAQ.", "startAdding": "Comienza agregando preguntas", "addTitle": "AGREGAR PREGUNTA", "editTitle": "EDITAR PREGUNTA", "questionLabel": "Preguntas", "questionPlaceholder": "Ingresa la pregunta", "questionEmpty": "La pregunta no puede estar vacía.", "atLeastOne": "Se requiere al menos una pregunta.", "answerLabel": "Respuesta", "answerPlaceholder": "Ingresa la respuesta", "answerEmpty": "La respuesta no puede estar vacía.", "linkFlowLabel": "<PERSON><PERSON><PERSON>", "chooseFlowPlaceholder": "Elegir flujo", "primaryLabel": "Principal", "questionPrefix": "P", "answerPrefix": "R", "questionsAdded": "Preguntas agregadas.", "questionsUpdated": "Preguntas actualizadas.", "maxQuestions": "<PERSON><PERSON><PERSON> agregar un máximo de {{count}} preguntas.", "questionsDeleted": "Preguntas eliminadas.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE FAQ", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar este elemento de FAQ?"}}, "fileUpload": {"fileTooLarge": "El archivo debe tener menos de {{size}} MB y ser de tipo {{type}}", "someFilesRejected": "Algunos archivos fueron rechazados. Asegúrate de que sean del tipo correcto y tamaño < {{size}} MB.", "failedToUpload": "Error al subir: {{filename}}"}, "tabs": {"contentComingSoon": "Contenido para la pestaña {{tabName}} próximamente"}, "builder": {"tabs": {"design": "Diseño", "train": "Entrenar", "channels": "Canales", "agentTransfer": "Transferencia a agente", "integrations": "Integraciones", "settings": "Configuración"}}, "flows": {"untitledFlow": "flujo sin título", "welcome": "Bienvenida", "fallback": "Alternativa", "targetFlow": "<PERSON><PERSON><PERSON>", "existingFlow": "Recordar contexto entre el flujo de conexión"}, "agentTransfer": {"transfer": "Integrar Agente desde la página ‘Agente de Transferencia’ para configurar el Agente Nativo", "selectAgentTransfer": "Seleccionar un Agente de Transferencia", "nothingSelected": "<PERSON>da se<PERSON>", "filters": {"all": "Todo", "native": "Nativo", "thirdParty": "Terceros"}, "tabs": {"available": "Disponibles", "myAgentTransfers": "Mis Agentes de Transferencia"}, "setupHeading": "Configurar", "setupDescription": "Proporciona los detalles a continuación para activar el soporte de {{agentTransferName}} para el chatbot.", "generateToken": "GENERAR TOKEN", "liveAgentPortalDetails": "Detalles del Portal del Agente en Vivo", "pendingStatus": "Pendiente", "remove": "ELIMINAR", "chatbotNameLabel": "Nombre del chatbot:", "accessTokenLabel": "Token de acceso:", "shareInstruction": "Comparte lo anterior con el Administrador del Agente para la activación."}, "settings": {"language": "Idioma", "nlu": "NLU", "personalization": "Personalización", "llmConfiguration": "Configuración LLM", "cannedResponses": "Respuestas prediseñadas", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor"}, "stencil": {"nodes": "Nodos", "searchNodes": "Buscar nodos..."}, "platform": {"web": "Web", "mobile": "Móvil"}, "pagination": {"previous": "Anterior", "next": "Siguient<PERSON>", "morePages": "<PERSON>ás páginas"}, "form": {"loadingForm": "Cargando formulario...", "typing": "Escribiendo..."}, "errors": {"failedToSend": "Error al enviar mensaje", "unexpectedResponse": "Respuesta inesperada del servidor", "somethingWrong": "Algo salió mal"}, "channels": {"selectWABA": "Selecciona un número WABA para conectar", "changeNumber": "CAMBIAR NÚMERO", "webhook": "Webhook", "webhookInstruction": "Pega este Webhook en el número WABA en el canal NGAGE WhatsApp para integrarlo.", "switchToMeta": "Cambiar a Meta Cloud API", "switchDescription": "Cambia a Meta Cloud API y vincula tu Chatbot mediante el BSP asociado.", "switch": "CAMBIAR", "connect": "CONECTAR", "selectChannels": "Selecciona los canales para configurar", "nothingSelected": "<PERSON>da se<PERSON>", "myChannels": "Mis canales", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voz", "alexa": "Alexa", "available": "Disponible", "invalid": "INVÁLIDO", "testChannel": "Canal de prueba", "getStarted": "COMENZAR", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "filters": {"all": "Todo", "native": "Nativo", "text": "Texto", "voice": "Voz"}, "tabs": {"available": "Disponibles", "myChannels": "Mis canales"}}, "nodes": {"agentTransfer": "Transferencia a agente", "appEnd": "Fin de app", "appStart": "Inicio de app", "choice": "Opción", "choiceOption": "Opción de elección", "feedback": "Retroalimentación", "flowConnector": "<PERSON><PERSON> de <PERSON>", "http": "HTTP", "interactiveMessage": "Mensaje interactivo", "language": "Idioma", "message": "Men<PERSON><PERSON>", "notification": "Notificación", "payment": "Pago", "script": "Guion", "text": "Texto", "waitDelay": "<PERSON><PERSON><PERSON> re<PERSON>o", "whatsapp": "WhatsApp"}, "bots": {"testBot": "<PERSON><PERSON>", "testChatbot": "<PERSON><PERSON><PERSON>", "aChatbot": "Un chatbot de prueba", "aChatbotDescription": "Descripción de un chatbot de prueba", "myFlow": "Mi flujo", "lastUpdatedToday": "Última actualización hoy"}, "whatsapp": {"onboarding": {"ngage": {"description": "Incorpora WABA usando el canal NGAGE WhatsApp e intégralo con tu Chatbot."}, "meta": {"description": "Incorpora WABA usando Meta Cloud API y vincula tu Chatbot vía el BSP asociado."}}}}