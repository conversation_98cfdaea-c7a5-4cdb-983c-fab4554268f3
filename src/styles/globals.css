@import './style.css';
@import 'react-quill/dist/quill.snow.css';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* .mfe-app {
  font-family: 'Poppins', sans-serif !important;
  font-weight: 400;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
@layer components {
  [data-radix-popper-content-wrapper] {
    z-index: 50 !important;
  }
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221, 83%, 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --tertiary: 0 0% 64.7%;
    --tertiary-foreground: 0 0% 98%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --error: 0 84.2% 60.2%;
    --error-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --tertiary: 0 0% 45.1%;
    --tertiary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --error: 0 62.8% 30.6%;
    --error-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-tertiary: var(--tertiary);
  --color-tertiary-foreground: var(--tertiary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-error: var(--error);
  --color-error-foreground: var(--error-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Stencil Drag Performance Optimizations */

/* Improve drag responsiveness by optimizing transitions and transforms */
.stencil-node {
  /* Use GPU acceleration for better performance */
  transform: translateZ(0);
  will-change: transform;

  /* Optimize transitions for drag operations */
  transition:
    transform 0.1s ease-out,
    opacity 0.1s ease-out;
}

/* Optimize drag feedback during pointer down */
.stencil-node:active {
  transform: translateZ(0) scale(0.98);
  opacity: 0.9;
  transition: none; /* Remove transition during active drag for immediate feedback */
}

/* Improve stencil paper drag performance */
.stencil-paper-drag {
  /* Use GPU acceleration */
  transform: translateZ(0);
  will-change: transform, opacity;

  /* Optimize rendering during drag */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  /* Improve opacity transition */
  transition: opacity 0.1s ease-out;
}

.stencil-paper-drag.dragging {
  /* Ensure smooth dragging */
  transform: translateZ(0);

  /* Optimize pointer events */
  pointer-events: none !important;
}

/* Optimize node item hover states */
.stencil-node-item {
  /* Use GPU acceleration */
  transform: translateZ(0);
  will-change: transform;

  /* Optimize hover transitions */
  transition:
    transform 0.15s ease-out,
    box-shadow 0.15s ease-out;
}

.stencil-node-item:hover:not(.cursor-not-allowed) {
  transform: translateZ(0) translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Optimize grid layout for better performance */
.stencil-grid {
  /* Use CSS containment for better performance */
  contain: layout style paint;

  /* Optimize scrolling */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Optimize image loading in stencil nodes */
.stencil-node img {
  /* Prevent layout shifts during image loading */
  display: block;

  /* Optimize image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* Use GPU acceleration */
  transform: translateZ(0);
}

/* Optimize React component rendering during drag */
.stencil-container {
  /* Use CSS containment */
  contain: layout style;

  /* Optimize repaints */
  isolation: isolate;
}

/* Disable transitions during drag operations for better performance */
body[data-dragging='true'] .stencil-node,
body[data-dragging='true'] .stencil-node-item {
  transition: none !important;
}

/* Optimize pointer capture feedback */
.stencil-node[data-pointer-captured='true'] {
  cursor: grabbing;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Improve visual feedback during drag initialization */
.stencil-node.drag-initializing {
  transform: translateZ(0) scale(0.95);
  opacity: 0.8;
  transition: none;
}

/* Optimize for touch devices */
@media (hover: none) and (pointer: coarse) {
  .stencil-node {
    /* Larger touch targets */
    min-height: 48px;
    min-width: 48px;

    /* Remove hover effects on touch devices */
    transition: none;
  }

  .stencil-node:active {
    transform: translateZ(0) scale(0.95);
    opacity: 0.7;
  }
}

/* Optimize for high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .stencil-node img {
    /* Better image quality on high DPI */
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .stencil-node,
  .stencil-node-item,
  .stencil-paper-drag {
    transition: none !important;
    animation: none !important;
  }
}
