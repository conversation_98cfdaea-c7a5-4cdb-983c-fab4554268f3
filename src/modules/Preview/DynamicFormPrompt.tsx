'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { Form } from '@/components/ui/form';
import { useForm, Controller } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/DatePicker/date-picker';
import TimePicker from '@/components/time-picker';
import { FormField, BotFormFields, ChatMessage } from './types/types';
import { DateStartRange, DateEndRange } from './types/constants';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { TimeFormat } from './types/enums';
import { zodResolver } from '@hookform/resolvers/zod';
import { createFormValidationSchema } from './schema'; // adjust path
import { format } from 'date-fns';

interface DynamicFormPromptProps {
  message: ChatMessage;
  formDisabled: boolean;
  onSubmit: (fields: BotForm<PERSON>ields) => Promise<void>;
  defaultValues?: BotFormFields;
}

const DynamicFormPrompt: React.FC<DynamicFormPromptProps> = ({
  message,
  formDisabled,
  onSubmit,
  defaultValues,
}) => {
  const { t } = useTranslation();
  const mergedDefaults = { ...defaultValues, ...message.data?.submittedValues };

  const promptFields: FormField[] = message.data?.prompt ?? [];
  const validationSchema = createFormValidationSchema(promptFields);
  const form = useForm<BotFormFields>({
    resolver: zodResolver(validationSchema),
    mode: 'onTouched', // ✅ only validate after user leaves the field
    reValidateMode: 'onChange', // ✅ revalidate when user edits
    defaultValues: mergedDefaults,
  });

  const { control, handleSubmit, formState } = form;
  return (
    <Form {...form}>
      <form
        className="space-y-3 p-2 mb-3 border border-muted-400 rounded-lg"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        {promptFields.map(field => (
          <div key={field.fieldName} className="relative">
            <label className="mb-1 font-medium text-tertiary-700 block">
              {field.label ?? field.fieldName}
              {field.required ? <span className="ml-1 text-error-600">*</span> : null}
            </label>
            {field.fieldType === 'time' ? (
              <Controller
                name={field.fieldName}
                control={control}
                rules={{ required: field.required }}
                render={({ field: rhfField }) => (
                  <TimePicker
                    disabled={formDisabled}
                    value={typeof rhfField.value === 'string' ? rhfField.value : ''}
                    onChange={rhfField.onChange}
                    format={TimeFormat.TWELVE_HOUR}
                    className={cn(
                      'w-full border-tertiary-200 text-tertiary-500',
                      formDisabled ? 'opacity-50 pointer-events-none' : ''
                    )}
                  />
                )}
              />
            ) : field.fieldType.includes('date') ? (
              <Controller
                name={field.fieldName}
                control={control}
                rules={{ required: field.required }}
                render={({ field: rhfField }) => (
                  <DatePicker
                    disabled={formDisabled}
                    selected={
                      rhfField.value
                        ? typeof rhfField.value === 'string'
                          ? new Date(rhfField.value)
                          : rhfField.value
                        : undefined
                    }
                    onSelect={d => rhfField.onChange(d ?? '')}
                    fieldType={field.fieldType}
                    customRange={
                      field.fieldType === 'custom_date'
                        ? {
                            start: new Date(field.rangeStart || DateStartRange),
                            end: new Date(field.rangeEnd || DateEndRange),
                          }
                        : undefined
                    }
                    className={cn(
                      'w-full max-w-full truncate border border-tertiary-200 text-tertiary-500 overflow-hidden',
                      formDisabled ? 'opacity-50 pointer-events-none' : ''
                    )}
                  />
                )}
              />
            ) : field.fieldType === 'mobile_number' ? (
              <Controller
                name={field.fieldName}
                control={control}
                render={({ field: rhfField }) => (
                  <div className="w-full !border h-10 !rounded-lg">
                    <PhoneInput
                      defaultCountry="in"
                      disabled={formDisabled}
                      value={rhfField.value || ''}
                      onChange={val => rhfField.onChange(val)} // ✅ Already gives full +<code>number
                      onBlur={rhfField.onBlur}
                      className="w-full !h-full !p-0.5 !rounded-lg"
                      inputClassName="w-full !h-full !border-0"
                      countrySelectorStyleProps={{ buttonClassName: '!border-0' }}
                    />
                  </div>
                )}
              />
            ) : (
              <Controller
                name={field.fieldName}
                control={control}
                rules={{ required: field.required }}
                render={({ field: rhfField }) => (
                  <Input
                    type={field.fieldType || 'text'}
                    placeholder={field.fieldName}
                    {...rhfField}
                    disabled={formDisabled}
                    className="w-full bg-white border border-tertiary-200 rounded-lg px-3 py-2.5 text-base placeholder-tertiary-400 focus:outline-none focus:ring-2 focus:ring-primary-300 "
                  />
                )}
              />
            )}
            {(formState.dirtyFields[field.fieldName] || formState.touchedFields[field.fieldName]) &&
              formState.errors[field.fieldName]?.message && (
                <p className="mt-1 text-xs text-error-600">
                  {formState.errors[field.fieldName]?.message as string}
                </p>
              )}
          </div>
        ))}

        <Button type="submit" className="mt-2 w-full" disabled={formDisabled || !formState.isValid}>
          {t('common.submit')}
        </Button>
      </form>
    </Form>
  );
};

export default DynamicFormPrompt;
