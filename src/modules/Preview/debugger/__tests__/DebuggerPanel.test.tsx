import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import DebuggerPanel from '../DebuggerPanel';
import { DebuggerEvent, DebuggerEventType, LogLevel } from '@/types/botInteraction.type';
import { ApiSliceIdentifier } from '@/store/helper';

// Mock i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: vi.fn((...args) => args.filter(Boolean).join(' ')),
}));

// Mock getBaseUrl
vi.mock('@/store/helper', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    getBaseUrl: vi.fn(() => 'http://localhost:8080'),
  };
});

// Mock child components and capture their props
const mockLogsComponent = vi.fn(() => <div>Logs Component</div>);
const mockAiAnalysisComponent = vi.fn(() => <div>AI Analysis Component</div>);
const mockSessionDataComponent = vi.fn(() => <div>Session Data Component</div>);

vi.mock('../Logs', () => ({ default: mockLogsComponent }));
vi.mock('../AiAnalysisLogs', () => ({ default: mockAiAnalysisComponent }));
vi.mock('../SessionData', () => ({ default: mockSessionDataComponent }));

// Mock EventSource
class MockEventSource {
  url: string;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  constructor(url: string) {
    this.url = url;
    MockEventSource.instances.push(this);
  }
  close = vi.fn();
  static instances: MockEventSource[] = [];
  static clearInstances() {
    MockEventSource.instances = [];
  }
  static dispatchMessage(data: any) {
    MockEventSource.instances.forEach(instance => {
      if (instance.onmessage) {
        instance.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
      }
    });
  }
  static dispatchError() {
    MockEventSource.instances.forEach(instance => {
      if (instance.onerror) {
        instance.onerror(new Event('error'));
      }
    });
  }
}
vi.stubGlobal('EventSource', MockEventSource);

describe('DebuggerPanel', () => {
  const mockOnOpenChange = vi.fn();
  const conversationId = 'test-conversation-id';
  const MIN_HEIGHT = 320;
  const MAX_HEIGHT = 950; // Assuming window.innerHeight is 1000, so 1000 - 50 = 950

  let mockOffsetHeight = MIN_HEIGHT;
  let mockClientHeight = MIN_HEIGHT;

  beforeEach(() => {
    vi.clearAllMocks();
    MockEventSource.clearInstances();

    // Mock window.innerHeight for consistent MAX_HEIGHT calculation
    Object.defineProperty(window, 'innerHeight', { writable: true, value: 1000 });

    // Mock offsetHeight and clientHeight for the panelRef
    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      configurable: true,
      get: function() {
        if (this.classList.contains('fixed')) { // Assuming the main panel div has 'fixed' class
          return mockOffsetHeight;
        }
        return 0; // Default for other elements
      },
    });
    Object.defineProperty(HTMLElement.prototype, 'clientHeight', {
      configurable: true,
      get: function() {
        if (this.classList.contains('fixed')) {
          return mockClientHeight;
        }
        return 0;
      },
    });
  });

  afterEach(() => {
    // Restore original properties after each test
    vi.restoreAllMocks();
  });

  it('renders correctly with initial elements', () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(screen.getByText('common.debugger')).toBeInTheDocument();
    expect(screen.getByLabelText('Close debugger panel')).toBeInTheDocument();
    expect(screen.getByText('debugger.logs')).toBeInTheDocument();
    expect(screen.getByText('debugger.aiAnalysis')).toBeInTheDocument();
    expect(screen.getByText('debugger.sessionData')).toBeInTheDocument();
    expect(screen.getByText('Logs Component')).toBeInTheDocument(); // Default tab content
  });

  it('calls onOpenChange when close button is clicked', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    await userEvent.click(screen.getByLabelText('Close debugger panel'));
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('switches tabs correctly', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    await userEvent.click(screen.getByText('debugger.aiAnalysis'));
    expect(screen.getByText('AI Analysis Component')).toBeInTheDocument();
    expect(screen.queryByText('Logs Component')).not.toBeInTheDocument();

    await userEvent.click(screen.getByText('debugger.sessionData'));
    expect(screen.getByText('Session Data Component')).toBeInTheDocument();
    expect(screen.queryByText('AI Analysis Component')).not.toBeInTheDocument();

    await userEvent.click(screen.getByText('debugger.logs'));
    expect(screen.getByText('Logs Component')).toBeInTheDocument();
    expect(screen.queryByText('Session Data Component')).not.toBeInTheDocument();
  });

  it('initializes EventSource with correct URL and handles messages', async () => {
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(MockEventSource.instances.length).toBe(1);
    const es = MockEventSource.instances[0];
    expect(es.url).toBe(`http://localhost:8080/debugger/stream/${conversationId}`);

    const mockLogEvent: DebuggerEvent = {
      type: DebuggerEventType.LOG,
      payload: { level: LogLevel.INFO, message: 'Test log message' },
      timestamp: Date.now(),
    };

    await act(async () => {
      MockEventSource.dispatchMessage(mockLogEvent);
    });

    // Verify that the Logs component received the new log
    expect(mockLogsComponent).toHaveBeenCalledWith(
      expect.objectContaining({
        logs: expect.arrayContaining([expect.objectContaining(mockLogEvent)]),
      }),
      {}
    );
  });

  it('handles EventSource errors', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const es = MockEventSource.instances[0];
    act(() => {
      MockEventSource.dispatchError();
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('EventSource failed:', expect.any(Event));
    expect(es.close).toHaveBeenCalledTimes(1);
    consoleErrorSpy.mockRestore();
  });

  it('closes EventSource on unmount', () => {
    const { unmount } = render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const es = MockEventSource.instances[0];
    unmount();
    expect(es.close).toHaveBeenCalledTimes(1);
  });

  it('does not initialize EventSource if conversationId is empty', () => {
    render(<DebuggerPanel conversationId="" onOpenChange={mockOnOpenChange} />);
    expect(MockEventSource.instances.length).toBe(0);
  });

  it('handles malformed EventSource messages gracefully', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const es = MockEventSource.instances[0];

    act(() => {
      if (es.onmessage) {
        es.onmessage(new MessageEvent('message', { data: 'invalid json' }));
      }
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('Error parsing debugger event:', expect.any(Error));
    consoleErrorSpy.mockRestore();
  });

  // Resizing tests
  it('resizes the panel height on mouse drag', async () => {
    const { container } = render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = container.querySelector('.fixed') as HTMLElement;

    if (!panel) throw new Error('Panel not found');

    // Set initial mocked height
    mockOffsetHeight = MIN_HEIGHT;
    panel.style.height = `${MIN_HEIGHT}px`; // Manually set style for initial check

    expect(panel.style.height).toBe(`${MIN_HEIGHT}px`);

    // Simulate mouse down
    fireEvent.mouseDown(resizeHandle, { clientY: 500 });

    // Simulate mouse move to increase height
    await act(async () => {
      fireEvent.mouseMove(document, { clientY: 400 }); // Dragging up by 100px
    });
    mockOffsetHeight = MIN_HEIGHT + 100; // Update mocked height after drag
    expect(panel.style.height).toBe(`${MIN_HEIGHT + 100}px`);

    // Simulate mouse move to decrease height
    await act(async () => {
      fireEvent.mouseMove(document, { clientY: 600 }); // Dragging down by 100px from original startY
    });
    mockOffsetHeight = MIN_HEIGHT - 100; // Update mocked height
    expect(panel.style.height).toBe(`${MIN_HEIGHT - 100}px`);

    // Simulate mouse up
    fireEvent.mouseUp(document);

    // Verify event listeners are removed
    await act(async () => {
      fireEvent.mouseMove(document, { clientY: 300 });
    });
    expect(panel.style.height).not.toBe(`${MIN_HEIGHT + 200}px`); // Should not change after mouseUp
  });

  it('respects MIN_HEIGHT during resizing', async () => {
    const { container } = render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = container.querySelector('.fixed') as HTMLElement;

    if (!panel) throw new Error('Panel not found');

    mockOffsetHeight = MIN_HEIGHT;
    panel.style.height = `${MIN_HEIGHT}px`;

    fireEvent.mouseDown(resizeHandle, { clientY: 500 });
    await act(async () => {
      fireEvent.mouseMove(document, { clientY: 800 }); // Drag far down to hit MIN_HEIGHT
    });
    mockOffsetHeight = MIN_HEIGHT; // Should be clamped at MIN_HEIGHT
    expect(panel.style.height).toBe(`${MIN_HEIGHT}px`);
    fireEvent.mouseUp(document);
  });

  it('respects MAX_HEIGHT during resizing', async () => {
    const { container } = render(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = container.querySelector('.fixed') as HTMLElement;

    if (!panel) throw new Error('Panel not found');

    mockOffsetHeight = MIN_HEIGHT; // Start from MIN_HEIGHT
    panel.style.height = `${MIN_HEIGHT}px`;

    fireEvent.mouseDown(resizeHandle, { clientY: 500 });
    await act(async () => {
      fireEvent.mouseMove(document, { clientY: 0 }); // Drag far up to hit MAX_HEIGHT
    });
    mockOffsetHeight = MAX_HEIGHT; // Should be clamped at MAX_HEIGHT
    expect(panel.style.height).toBe(`${MAX_HEIGHT}px`);
    fireEvent.mouseUp(document);
  });
});