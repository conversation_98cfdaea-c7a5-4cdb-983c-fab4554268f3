import React, { useCallback, useState } from 'react';
import { cn } from '@/lib/utils';
import { getModuleIcon, getModuleText } from '@/modules/editor/utils/config';
import { StencilNodesType } from '../../utils/constants';

interface NodeItemProps {
  type: StencilNodesType; // Use StencilNodesType for stricter typing
  startDrag: (event: React.PointerEvent, type: string) => void;
  isComingSoon?: boolean;
}

const NodeItem: React.FC<NodeItemProps> = ({ type, startDrag, isComingSoon }) => {
  const [isDragInitializing, setIsDragInitializing] = useState(false);

  const handlePointerDown = useCallback(
    (event: React.PointerEvent) => {
      if (isComingSoon) return;

      // Prevent default behavior and stop propagation for better responsiveness
      event.preventDefault();
      event.stopPropagation();

      // Set pointer capture for better drag tracking
      const target = event.currentTarget as HTMLElement;
      target.setPointerCapture(event.pointerId);
      target.setAttribute('data-pointer-captured', 'true');

      // Add drag initializing state for visual feedback
      setIsDragInitializing(true);
      document.body.setAttribute('data-dragging', 'true');

      // Start drag immediately
      startDrag(event, type);

      // Clean up after a short delay
      setTimeout(() => {
        setIsDragInitializing(false);
        target.removeAttribute('data-pointer-captured');
        document.body.removeAttribute('data-dragging');
      }, 150);
    },
    [startDrag, type, isComingSoon]
  );

  return (
    <div
      className={cn(
        'stencil-node-item rounded-lg hover:border-primary-300 transition-all cursor-pointer bg-white flex flex-col gap-3 w-12',
        {
          'opacity-50 select-none cursor-not-allowed': isComingSoon,
          'drag-initializing': isDragInitializing,
        }
      )}
      onPointerDown={handlePointerDown}
    >
      <div className="rounded-2xl flex items-center justify-center bg-primary-500/5 p-3 text-primary-600 flex-shrink-0 stencil-node">
        {typeof getModuleIcon(type) === 'string' ? (
          <img
            src={getModuleIcon(type) as string}
            className="text-primary-500 !w-6 !h-6"
            alt={type}
          />
        ) : (
          <div className=" border py-3 px-3  text-primary-500 rounded-2xl">
            {React.createElement(getModuleIcon(type) as React.ElementType, {
              size: 20,
            })}
          </div>
        )}
      </div>

      <p className="text-xs text-center text-wrap text-primary-600 w-full">{getModuleText(type)}</p>
    </div>
  );
};

export default NodeItem;
