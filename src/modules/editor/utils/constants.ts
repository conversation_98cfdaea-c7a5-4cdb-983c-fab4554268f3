export const StencilNodes = {
  APP_START: 'appStart',
  APP_END: 'appEnd',
  WHATSAPP: 'whatsapp',
  AGENT_TRANSFER: 'agentTransfer',
  HTTP: 'http',
  CHOICE: 'choice',
  FLOW_CONNECTOR: 'flowConnector',
  LANGUAGE: 'language',
  PAYMENT: 'payment',
  SCRIPT: 'script',
  WAIT_DELAY: 'waitDelay',
  MESSAGE: 'message',
  INTERACTIVE_MESSAGE: 'interactiveMessage',
  FEEDBACK: 'feedback',
  NOTIFICATION: 'notification',
  FORM: 'form',
} as const;

export const ExtendedStencilNodes = {
  ...StencilNodes,
  CHOICE_OPTION: 'choiceOption',
} as const;

export type StencilNodesType = (typeof StencilNodes)[keyof typeof StencilNodes];
export type ExtendedStencilNodesType =
  (typeof ExtendedStencilNodes)[keyof typeof ExtendedStencilNodes];

export const StencilTabs = {
  ENGAGE: 'Engage',
  UTILITIES: 'Utilities',
  MARKETPLACE: 'Marketplace',
};

export type StencilTabsType = (typeof StencilTabs)[keyof typeof StencilTabs];

export enum PlatformType {
  Web = 'web',
  // WhatsApp = 'whatsapp',
}

export enum MessageLanguage {
  English = 'English',
  Spanish = 'Spanish',
  French = 'French',
}

export const platformOptions = [
  { value: PlatformType.Web, label: 'Web' },
  { value: 'mobile', label: 'mobile' },
];

export enum FileType {
  IMAGE = 'image',
  FILE = 'file',
  VIDEO = 'video',
}

export const jointJsEditorTheme = {
  primaryColor: '#3b82f6',
  successColor: '#31D88A',
  errorColor: 'red',
  gridColor: 'rgba(118, 72, 219, 0.40)',
  backgroundColor: 'white',
};
