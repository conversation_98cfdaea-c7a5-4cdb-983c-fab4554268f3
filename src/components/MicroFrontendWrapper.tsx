import '../styles/globals.css';
import '@/lib/i18n/config';
import { cn } from '@/lib/utils';
import React, { forwardRef } from 'react';

const MicroFrontendWrapper = forwardRef<
  HTMLDivElement,
  React.PropsWithChildren<{ className?: string }>
>(({ children, className }, ref) => {
  return (
    <div ref={ref} className={cn('mfe-app h-full w-full', className)}>
      {children}
    </div>
  );
});

export default MicroFrontendWrapper;
