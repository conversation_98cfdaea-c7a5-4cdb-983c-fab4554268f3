import { But<PERSON> } from '@/components/ui/button';
import { LoaderCircle } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, useFormContext } from 'react-hook-form';

interface RenderButtonsProps {
  handleClose: () => void;
  handleAddClick: () => void;
  isEdit?: boolean;
}

function RenderButtons({ handleClose, handleAddClick, isEdit }: RenderButtonsProps) {
  const { t } = useTranslation();
  const { formState } = useFormContext();
  const isLoading = formState.isSubmitting;
  const disabled = formState.isSubmitting || (!formState.isDirty && !isEdit);

  return (
    <div className="flex justify-end gap-2 py-4">
      <Button
        className="w-28 h-10"
        variantColor={'tertiary'}
        variant="outline"
        onClick={handleClose}
      >
        {t('common.cancel')}
      </Button>
      <Button
        className="w-28 h-10"
        type="submit"
        aria-label="Form Submit Button"
        disabled={disabled}
        onClick={handleAddClick}
      >
        {isLoading && <LoaderCircle className="w-4 h-4 animate-spin" />}
        {isEdit ? t('common.update') : t('common.add')}
      </Button>
    </div>
  );
}

export default RenderButtons;
