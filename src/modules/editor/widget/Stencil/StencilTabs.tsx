import React from 'react';
import { nodePanelTabs } from '@/modules/editor/utils/config';

interface StencilTabsProps {
  currentTab: string;
  handleTabChange: (tab: string) => void;
}

const StencilTabs: React.FC<StencilTabsProps> = ({ currentTab, handleTabChange }) => {
  return (
    <div className="flex justify-center items-center space-x-2 mt-4">
      {nodePanelTabs.map(({ key, name }) => (
        <button
          key={name}
          onClick={() => handleTabChange(key)}
          className={`px-3 py-1.5 rounded-full border text-xs ${
            key === currentTab
              ? 'bg-primary-600 text-white'
              : 'text-primary-600 hover:bg-primary-100'
          }`}
        >
          {name}
        </button>
      ))}
    </div>
  );
};

export default StencilTabs;
