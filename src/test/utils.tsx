import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import './setup';
import { MemoryRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

import { Provider } from 'react-redux';
import { apiSlice } from '@/store/apiSlice';
import authReducer from '@/store/auth/authSlice';
import type { RootState } from '../store/store';

// Export React for global access
export { React };

interface IProvidersProps {
  initialEntries?: string[];
  children: React.ReactNode;
  preloadedState?: Partial<RootState>;
  store?: any;
}

// Custom render function
const AllTheProviders = ({ children, initialEntries, preloadedState, store }: IProvidersProps) => {
  const defaultPreloadedState: Partial<RootState> = {
    auth: {
      accessToken: 'mockAccessToken',
      refreshToken: 'mockRefreshToken',
      expires: **********,
      isLoggedIn: true,
    },
    ...(preloadedState || {}),
  };

  const testStore =
    store ||
    configureStore({
      reducer: { [apiSlice.reducerPath]: apiSlice.reducer, auth: authReducer },
      middleware: getDefaultMiddleware => getDefaultMiddleware().concat(apiSlice.middleware),
      preloadedState: defaultPreloadedState,
    });

  return (
    <Provider store={testStore}>
      <MemoryRouter initialEntries={initialEntries}>{children}</MemoryRouter>
    </Provider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & {
    preloadedState?: Partial<RootState>;
    store?: any;
  }
) => {
  const { preloadedState, store, ...renderOptions } = options || {};
  return render(ui, {
    wrapper: props => <AllTheProviders {...props} preloadedState={preloadedState} store={store} />,
    ...renderOptions,
  });
};

export * from '@testing-library/react';
export { customRender as render };