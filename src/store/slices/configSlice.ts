import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface ConfigData {
  botBuilderApiUrl: string;
  botInteractionApiUrl: string;
}

interface ConfigState {
  configData: ConfigData | null;
  loading: boolean;
  error: string | null;
}

const initialState: ConfigState = {
  configData: null,
  loading: false,
  error: null,
};

export const fetchConfig = createAsyncThunk(
  'config/fetchConfig',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/config.json');
      if (!response.ok) {
        throw new Error('Failed to fetch config.json');
      }
      const data = await response.json();
      return data as ConfigData;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchConfig.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConfig.fulfilled, (state, action) => {
        state.loading = false;
        state.configData = action.payload;
      })
      .addCase(fetchConfig.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default configSlice.reducer;
