import React from 'react';
import SettingsGrid from '@/components/settings-grid';
import ChannelsTab from '@/modules/Channels/index';
import AgentTransfersTab from '@/modules/AgentTransfer/index';
import TrainTabContent from '@/modules/train';

import { Settings } from 'lucide-react';
import { TabConfig } from '@/types';
import NeuratalkEditorWrapper from '@/modules/editor/EditorWrapper';

export const neuraTalkBuilderTabs: TabConfig[] = [
  { id: 'Design', labelKey: 'builder.tabs.design', Component: NeuratalkEditorWrapper },
  { id: 'Train', labelKey: 'builder.tabs.train', Component: TrainTabContent },
  { id: 'Channels', labelKey: 'builder.tabs.channels', Component: ChannelsTab },
  {
    id: 'Agent Transfer',
    labelKey: 'builder.tabs.agentTransfer',
    Component: AgentTransfersTab,
  },
  { id: 'Integrations', labelKey: 'builder.tabs.integrations' },
  {
    id: 'Settings',
    labelKey: 'builder.tabs.settings',
    icon: <Settings className="mr-1" />,
    Component: SettingsGrid,
  },
];
