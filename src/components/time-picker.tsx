import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Clock } from 'lucide-react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Meridian, TimeFormat } from '@/modules/Preview/types/enums';
import { cn } from '@/lib/utils';
import { pad } from '@/utils/getPad';

type TimePickerProps = {
  value: string;
  format?: TimeFormat;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
};

// Convert 12h + meridian to 24h and vice versa
const formatDisplay = (h: number, m: number, meridian?: Meridian, format?: TimeFormat): string =>
  format === TimeFormat.TWELVE_HOUR
    ? `${pad(h % 12 || 12)}:${pad(m)} ${meridian ?? Meridian.AM}`
    : `${pad(h)}:${pad(m)}`;

const safeParseTime = (
  raw: string,
  format: TimeFormat
): { h: number; m: number; meridian?: Meridian } => {
  if (!raw || typeof raw !== 'string') {
    return format === TimeFormat.TWELVE_HOUR
      ? { h: 12, m: 0, meridian: Meridian.AM }
      : { h: 0, m: 0 };
  }
  const [time, maybeMeridian] = raw.trim().split(' ');
  const [h = '0', m = '0'] = time.split(':').map(Number) as [number, number];
  const meridian =
    (maybeMeridian as Meridian | undefined) ||
    (format === TimeFormat.TWELVE_HOUR ? Meridian.AM : undefined);
  const hours = Math.min(
    Math.max(0, isNaN(h) ? 0 : h),
    format === TimeFormat.TWELVE_HOUR ? 12 : 23
  );
  const minutes = Math.min(Math.max(0, isNaN(m) ? 0 : m), 59);

  if (format === TimeFormat.TWELVE_HOUR) {
    const baseHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return {
      h: baseHours,
      m: minutes,
      meridian: meridian === Meridian.PM ? Meridian.PM : Meridian.AM,
    };
  }
  return { h: hours, m: minutes };
};

const TimePicker: React.FC<TimePickerProps> = ({
  value = '',
  format = TimeFormat.TWELVE_HOUR,
  onChange,
  disabled,
  className = '',
}) => {
  // Memoized parsed time to avoid recalculating on every render
  const { h, m, meridian } = useMemo(() => safeParseTime(value, format), [value, format]);

  // State for temporary meridian (only for 12-hour format)
  const [tempPeriod, setTempPeriod] = useState<Meridian>(meridian ?? Meridian.AM);

  // Sync tempPeriod with value on mount or format change
  useEffect(() => {
    if (format === TimeFormat.TWELVE_HOUR) setTempPeriod(meridian ?? Meridian.AM);
  }, [meridian, format]);

  // Initialize value if empty
  useEffect(() => {
    if (!value.trim()) {
      onChange(format === TimeFormat.TWELVE_HOUR ? '12:00 AM' : '00:00');
    }
  }, [value, onChange, format]);

  // Memoized time update function to prevent recreation
  const updateTime = useCallback(
    (newH: number, newM: number, period?: Meridian) => {
      if (disabled) return;
      const formatted = formatDisplay(
        newH,
        newM,
        period ?? (format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined),
        format
      );
      onChange(formatted);
    },
    [disabled, format, tempPeriod, onChange]
  );

  // Memoized option arrays for performance
  const hoursArr = useMemo(
    () =>
      Array.from({ length: format === TimeFormat.TWELVE_HOUR ? 12 : 24 }, (_, i) =>
        format === TimeFormat.TWELVE_HOUR ? i + 1 : i
      ),
    [format]
  );
  const minutesArr = useMemo(() => Array.from({ length: 12 }, (_, i) => i * 5), []); // Optimized to 0-55 step 5

  // UI rendering (unchanged)
  return (
    <Popover>
      <PopoverTrigger disabled={disabled} asChild>
        <div
          className={cn(
            'flex items-center justify-between px-3 py-2 border border-tertiary-300 rounded-md bg-white cursor-pointer',
            disabled && 'opacity-50 pointer-events-none cursor-not-allowed',
            className
          )}
          tabIndex={disabled ? -1 : 0}
          aria-disabled={disabled}
        >
          <span className="text-sm text-[var(--text-default)]">
            {formatDisplay(
              h,
              m,
              format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined,
              format
            )}
          </span>
          <Clock className="w-4 h-4 text-tertiary-500" />
        </div>
      </PopoverTrigger>

      <PopoverContent
        className={cn(
          'p-2 grid grid-cols-3 text-center gap-2',
          disabled && 'pointer-events-none opacity-60'
        )}
      >
        {/* Hours */}
        <div className="overflow-y-auto max-h-40">
          <div className="font-semibold text-xs mb-1">Hour</div>
          {hoursArr.map(hourVal => (
            <div
              key={hourVal}
              className="text-sm p-1 rounded hover:bg-tertiary-200 cursor-pointer"
              onClick={() =>
                updateTime(hourVal, m, format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined)
              }
            >
              {pad(hourVal)}
            </div>
          ))}
        </div>
        {/* Minutes */}
        <div className="overflow-y-auto max-h-40">
          <div className="font-semibold text-xs mb-1">Min</div>
          {minutesArr.map(minVal => (
            <div
              key={minVal}
              className="text-sm p-1 rounded hover:bg-tertiary-200 cursor-pointer"
              onClick={() =>
                updateTime(h, minVal, format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined)
              }
            >
              {pad(minVal)}
            </div>
          ))}
        </div>
        {/* AM/PM */}
        {format === TimeFormat.TWELVE_HOUR && (
          <div>
            <div className="font-semibold text-xs mb-1">AM/PM</div>
            {[Meridian.AM, Meridian.PM].map(p => (
              <div
                key={p}
                className={cn(
                  'text-sm p-1 rounded cursor-pointer hover:bg-tertiary-200',
                  p === tempPeriod && 'bg-tertiary-300'
                )}
                onClick={() => {
                  setTempPeriod(p);
                  updateTime(h, m, p);
                }}
              >
                {p}
              </div>
            ))}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default TimePicker;
