import React, { useState, useCallback } from 'react';
import { 
  useGetFaqCategoriesInfiniteQuery, 
  useUpdateFaqCategoryMutation,
  useDeleteFaqCategoryMutation,
  faqApiHelpers 
} from '@/store/api/faqApi';
import { useRTKInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { PaginationParams } from '@/types';

interface OptimizedFaqListProps {
  searchTerm?: string;
  limit?: number;
}

export const OptimizedFaqList: React.FC<OptimizedFaqListProps> = ({ 
  searchTerm = '', 
  limit = 20 
}) => {
  const [params] = useState<PaginationParams>({
    page: 1,
    limit,
    search: searchTerm,
  });

  // Use the infinite scroll version of the query
  const queryResult = useGetFaqCategoriesInfiniteQuery(params);
  const [updateFaqCategory] = useUpdateFaqCategoryMutation();
  const [deleteFaqCategory] = useDeleteFaqCategoryMutation();

  // Set up infinite scroll
  const { loadMoreRef, isLoadingMore } = useRTKInfiniteScroll(
    queryResult,
    faqApiHelpers.hasMoreFaqCategories,
    faqApiHelpers.getNextFaqCategoriesPage,
    queryResult.refetch,
    params
  );

  // Optimistic update handler
  const handleUpdate = useCallback(async (id: string, updates: any) => {
    try {
      await updateFaqCategory({ id, ...updates }).unwrap();
      // The cache is automatically updated via optimistic updates
    } catch (error) {
      console.error('Failed to update FAQ category:', error);
    }
  }, [updateFaqCategory]);

  // Optimistic delete handler
  const handleDelete = useCallback(async (id: string) => {
    try {
      await deleteFaqCategory({ id }).unwrap();
      // The cache is automatically updated via optimistic updates
    } catch (error) {
      console.error('Failed to delete FAQ category:', error);
    }
  }, [deleteFaqCategory]);

  if (queryResult.isLoading && !queryResult.data) {
    return <div>Loading...</div>;
  }

  if (queryResult.error) {
    return <div>Error loading FAQ categories</div>;
  }

  const items = queryResult.data?.data?.items || [];

  return (
    <div className="faq-list">
      <h2>FAQ Categories (Optimized)</h2>
      
      {items.map((category, index) => (
        <div key={category.id} className="faq-item">
          <h3>{category.name}</h3>
          <p>{category.description}</p>
          
          <div className="actions">
            <button 
              onClick={() => handleUpdate(category.id, { 
                name: category.name + ' (Updated)' 
              })}
            >
              Update
            </button>
            <button 
              onClick={() => handleDelete(category.id)}
              className="delete-btn"
            >
              Delete
            </button>
          </div>
          
          {/* Load more trigger - attach to last few items */}
          {index === items.length - 3 && (
            <div ref={loadMoreRef} className="load-more-trigger" />
          )}
        </div>
      ))}
      
      {isLoadingMore && (
        <div className="loading-more">Loading more...</div>
      )}
      
      {!faqApiHelpers.hasMoreFaqCategories(queryResult.data) && items.length > 0 && (
        <div className="end-message">No more items to load</div>
      )}
    </div>
  );
};