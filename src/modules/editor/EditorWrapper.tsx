import React, { useEffect, useState } from 'react';
import FlowsPanel from './widget/flowsPanel';
import NeuraTalkEditor from './neuratalk-editor';
import { useSelector, useDispatch } from 'react-redux';
import { setActiveFlow } from '@/store/slices/flowsSlice';
const EditorWrapper = () => {
  const dispatch = useDispatch();
  const selectedFlow = useSelector(state => state.flows.activeFlowId);
  const onSelectFlow = selectedFlow => {
    dispatch(setActiveFlow(selectedFlow));
  };

  useEffect(() => {
    return () => {
      //TODO: rm this after implementing default flo
      dispatch(setActiveFlow(''));
    };
  }, []);

  return (
    <div className='relative transform'>
      <FlowsPanel onFlowSelect={onSelectFlow} />
      <NeuraTalkEditor id={selectedFlow.appId} />
    </div>
  );
};

export default EditorWrapper;
