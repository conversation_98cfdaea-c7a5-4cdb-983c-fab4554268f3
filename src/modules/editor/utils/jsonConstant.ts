import { Position, ChoiceDetails, PortGroup } from '../types';
import { getModuleIcon, getModuleText } from './config';
import { v4 as uuidv4 } from 'uuid';
import { jointJsEditorTheme } from './constants';

export const linksConstant = Object.freeze({
  type: 'standard.Link',
  source: {
    // "id": "fd6eb20c-ff08-47ae-9348-264cf562a491",
    magnet: 'portBody',
    // "port": "b03ec9b5-fab0-4f48-85ee-c7944c73951b"
  },
  target: {
    // "id": "811cf871-00f7-4f8a-92de-def1f23dcb29"
  },
  router: {
    name: 'manhattan',
    elementPadding: 10,
    padding: 50,
  },
  connector: {
    name: 'rounded',
  },
  z: 0,
  // "id": "b7da1f2f-5197-44f9-ae2f-23c6985ffda4",
  attrs: {
    line: {
      stroke: jointJsEditorTheme.primaryColor,
      strokeWidth: 1,
      class: 'animate-link',
    },
  },
});

// Common port groups
const commonPortGroups: { in: PortGroup; out: PortGroup } = {
  in: {
    position: { name: 'top', args: { x: 25, y: 0 } },
    attrs: {
      portBody: {
        magnet: true,
        r: 4,
        fill: 'transparent',
        stroke: 'transparent',
      },
    },
    label: {
      position: { name: 'top', args: { x: 25, y: 50 } },
      markup: [{ tagName: 'text', selector: 'label', className: 'label-text' }],
    },
    markup: [{ tagName: 'circle', selector: 'portBody' }],
  },
  out: {
    position: { name: 'bottom', args: { x: 25, y: 50 } },
    attrs: {
      portBody: {
        magnet: true,
        r: 4,
        fill: jointJsEditorTheme.primaryColor,
        stroke: jointJsEditorTheme.primaryColor,
        x: -8,
        y: -8,
      },
    },
    label: {
      position: { name: 'right', args: { y: 6 } },
      markup: [{ tagName: 'text', selector: 'label', className: 'label-text' }],
    },
    markup: [{ tagName: 'circle', selector: 'portBody' }],
  },
};

// Choice-specific out port group (uses green color for distinction)
const choiceOutPort: PortGroup = {
  ...commonPortGroups.out,
  position: { name: 'bottom', args: { x: 15, y: 50 } },
  attrs: {
    portBody: {
      magnet: true,
      r: 4,
      fill: '#31D88A',
      stroke: '#31D88A',
      x: -8,
      y: -8,
    },
  },
};

export const getNodeConstant = (
  { type, name, image }: { type: string; name?: string; image?: string },
  position: Position,
  nodeId: string,
  z: number,
  choiceDetails?: ChoiceDetails
) => {
  // Generate unique port ID
  const generatePortId = () => uuidv4().slice(0, 7);

  // Handle choiceOption node
  if (type === 'choiceOption' && choiceDetails) {
    const { condition, value } = choiceDetails;
    const maxLen = Math.max(condition.length, value.length);
    return {
      type,
      size: { height: 60, width: maxLen * 9 },
      position,
      angle: 0,
      ports: {
        groups: {
          in: commonPortGroups.in,
          out: {
            ...commonPortGroups.out,
            position: { name: 'bottom', args: { x: 'calc(0.5*w)', y: 60 } },
          },
        },
        items: [
          { group: 'out', id: generatePortId() },
          { group: 'in', id: generatePortId() },
        ],
      },
      id: nodeId,
      z,
      attrs: {
        text: { text: condition, x: 'calc(0.5*w)', y: 15 },
        text1: { text: value },
        rect1: { width: maxLen * 7 },
      },
    };
  }

  // Handle choice node
  if (type === 'choice') {
    return {
      type,
      size: { width: 300, height: 50.93877551020408 },
      position,
      angle: 0,
      ports: {
        groups: {
          in: commonPortGroups.in,
          out: choiceOutPort,
        },
        items: [
          { group: 'out', id: generatePortId() },
          { group: 'in', id: generatePortId() },
          { group: 'out', id: generatePortId() },
        ],
      },
      id: nodeId,
      z,
      attrs: {
        image: {
          'xlink:href': image || getModuleIcon(type),
          width: 35,
          height: 35,
          x: 7,
          y: 7,
          fill: jointJsEditorTheme.primaryColor,
        },
        text: { text: name || getModuleText(type), x: 60, y: 29 },
      },
    };
  }

  // Handle all other nodes
  return {
    type,
    size: { width: 300, height: 50.93877551020408 },
    position,
    angle: 0,
    ports: {
      groups: commonPortGroups,
      items: [
        ...(type === 'appEnd' ? [] : [{ group: 'out', id: generatePortId() }]),
        { group: 'in', id: generatePortId() },
      ],
    },
    id: nodeId,
    z,
    attrs: {
      image: {
        'xlink:href': image || getModuleIcon(type),
        width: 35,
        height: 35,
        x: 7,
        y: 7,
        style: jointJsEditorTheme.primaryColor,
      },
      text: { text: name || getModuleText(type), x: 60, y: 29 },
    },
  };
};

// Common output structure for most nodes
const defaultOutput = {
  codeModuleMapping: [
    { code: '200', moduleId: 'aaaaaa' },
    { code: '400', moduleId: 'aaaaab' },
  ],
  conditions: {
    '8ae5937d71': {
      statement: [{ expr: ['', 'eq', ''] }],
      fallbackcode: '',
      isActive: true,
    },
  },
  fallbackcode: '',
  codeActive: true,
  customCode: `// Custom code panel
// main function is the default method executed after processing current module
function main() {
  return "84d6645"; // return end moduleId
}`,
  customCodeIds: { conditionalLink: [] },
};

// Common node data structure for coordinates
const getNodeData = (title: string, name: string, moduleType: string, options = {}) => ({
  title,
  name,
  id: '0ca30af',
  isEditable: true,
  canDelete: true,
  status: '',
  moduleType,
  ...options,
});

// Base coordinates structure
const defaultCoordinates = (
  title: string,
  name: string,
  moduleType: string,
  x = 427.796875,
  y = 319,
  options = {}
) => ({
  x,
  y,
  nodeData: getNodeData(title, name, moduleType, options),
});

export const leapJsonConstant = {
  appStart: {
    settings: { aparty: '', nodeName: getModuleText('appStart') },
    process: { cronjob: '', params: [], trigger: '' },
    output: {
      conditions: {
        '82a27eabb4': {
          statement: [{ expr: ['', 'eq', ''] }],
          fallbackcode: '',
          isActive: true,
        },
      },
      fallbackcode: '',
      codeActive: true,
      customCode: `// Custom code panel
// main function is the default method executed after processing current module
function main() {
  return "ebf9288"; // return end moduleId
}`,
      customCodeIds: { conditionalLink: [] },
    },
    input: {},
    type: 'appStart',
    typeId: '0.1',
    coordinates: defaultCoordinates('Trigger', 'App Start', 'appStart', 107.796875, 100, {
      canDelete: false,
    }),
  },
  appEnd: {
    settings: { nodeName: getModuleText('appEnd') },
    process: {
      success: { code: [], message: '' },
      customErrors: [{ code: [], message: '' }],
      defaultError: { code: 'E9000', message: '' },
    },
    output: { conditions: {} },
    input: {},
    type: 'appEnd',
    typeId: '0.2',
    coordinates: defaultCoordinates('End', 'App End', 'appEnd', 709.796875, 449, {
      canDelete: false,
    }),
  },
  whatsapp: {
    settings: { nodeName: getModuleText('whatsapp') },
    process: { senderId: '', receiverNumber: '', message: '' },
    output: {
      ...defaultOutput,
      customCode: `// Custom code panel
// main function is the default method executed after processing current module
function main() {
  return "0ca30af"; // return end moduleId
}`,
      conditions: {
        a9d6649e12: {
          statement: [{ expr: ['', 'eq', ''] }],
          fallbackcode: '',
          isActive: true,
        },
      },
    },
    input: {},
    type: 'whatsapp',
    typeId: '1.92',
    coordinates: defaultCoordinates('Whatsapp', 'Whatsapp', 'whatsapp', 233.796875, 198),
  },
  agentTransfer: {
    settings: { nodeName: getModuleText('agentTransfer') },
    process: { agentId: '', queueId: '', transferReason: '' },
    output: defaultOutput,
    input: {},
    type: 'agentTransfer',
    typeId: '1.3',
    coordinates: defaultCoordinates('Agent Transfer', 'Agent Transfer', 'agentTransfer'),
  },
  http: {
    settings: {
      timeout: 10000,
      title: 'HTTP',
      nodeName: getModuleText('http'),
      image: '',
    },
    process: {
      URL: '',
      requestType: '',
      headers: [{ headerKey: '', headerValue: '' }],
      requestBody: '',
      responseCache: '',
      callReference: '',
      responseType: '',
      Value: '',
    },
    output: defaultOutput,
    input: {},
    type: 'http',
    typeId: '1.2',
    coordinates: defaultCoordinates('HTTP', 'HTTP Push', 'http'),
  },
  choice: {
    settings: { nodeName: getModuleText('choice') },
    process: { match_conditions: [], no_match_module_id: null },
    output: {},
    isChoiceLinked: false,
    input: {},
    type: 'choice',
    typeId: '0.9',
    coordinates: defaultCoordinates('Choice', 'choice', 'choice', 800, 270, {
      canDelete: false,
    }),
  },
  flowConnector: {
    settings: { nodeName: getModuleText('flowConnector') },
    process: { targetFlowId: '' },
    output: defaultOutput,
    input: {},
    type: 'flowConnector',
    typeId: '1.4',
    coordinates: defaultCoordinates('Flow Connector', 'Flow Connector', 'flowConnector'),
  },
  language: {
    settings: { nodeName: getModuleText('language') },
    process: { languageCode: '' },
    output: defaultOutput,
    input: {},
    type: 'language',
    typeId: '1.5',
    coordinates: defaultCoordinates('Language', 'Language', 'language'),
  },
  payment: {
    settings: { nodeName: getModuleText('payment') },
    process: { amount: '', currency: '', paymentMethod: '', merchantId: '' },
    output: defaultOutput,
    input: {},
    type: 'payment',
    typeId: '1.6',
    coordinates: defaultCoordinates('Payment', 'Payment', 'payment'),
  },
  script: {
    settings: { nodeName: getModuleText('script') },
    process: { code: '', connectedNodes: [] },
    output: defaultOutput,
    input: {},
    type: 'script',
    typeId: '1.94',
    coordinates: defaultCoordinates('Script Node', 'Script', 'script'),
  },
  waitDelay: {
    settings: { nodeName: getModuleText('waitDelay') },
    process: { delaySeconds: '' },
    output: defaultOutput,
    input: {},
    type: 'waitDelay',
    typeId: '1.7',
    coordinates: defaultCoordinates('Wait', 'Wait', 'waitDelay'),
  },
  message: {
    type: 'message',
    typeId: '2.1',
    name: 'Message',
    title: 'Message Handler',
    description: 'This module is used to handle conversation messages',
    settings: {
      enableLogging: true,
    },
    input: {},
    process: {
      web: {
        english: { messageText: '', images: [], videos: [], files: [] },
        hindi: { messageText: '', images: [], videos: [], files: [] },
        german: { messageText: '', images: [], videos: [], files: [] },
        arabic: { messageText: '', images: [], videos: [], files: [] },
      },
      whatsapp: {
        english: { messageText: '', images: [], videos: [], files: [] },
        hindi: { messageText: '', images: [], videos: [], files: [] },
        german: { messageText: '', images: [], videos: [], files: [] },
        arabic: { messageText: '', images: [], videos: [], files: [] },
      },
    },
    output: defaultOutput,
    coordinates: defaultCoordinates('Message Handler', 'Message', 'message'),
    definitions: {
      languageContent: {
        messageText: '',
        images: [],
        videos: [],
        files: [],
      },
    },
  },

  form: {
    typeId: '2.2',
    name: 'Form',
    title: 'Form Handler',
    description: 'This module is used to handle conversation forms with multi-field support',
    type: 'form',
    settings: {
      formType: 'survey',
      validateRequired: true,
    },
    input: {},
    process: {
      formId: 'df98bec',
      channelData: {
        web: {
          english: {
            formConfig: {
              formType: 'multi-ask-form',
              fields: [{ name: 'user_name', type: 'text', label: 'Provide your name' }],
            },
          },
          hindi: { formConfig: { formType: 'multi-ask-form', fields: [] } },
          german: { formConfig: { formType: 'multi-ask-form', fields: [] } },
          arabic: { formConfig: { formType: 'multi-ask-form', fields: [] } },
        },
        whatsapp: {
          english: { formConfig: { formType: 'multi-ask-form', fields: [] } },
          hindi: { formConfig: { formType: 'multi-ask-form', fields: [] } },
          german: { formConfig: { formType: 'multi-ask-form', fields: [] } },
          arabic: { formConfig: { formType: 'multi-ask-form', fields: [] } },
        },
      },
    },
    output: defaultOutput,
    coordinates: {
      id: 'node123',
      type: 'form',
      ports: [],
      nodedata: { title: 'Form Handler', name: 'Form', id: 'node123' },
    },
    definitions: {},
    version: '1.6.13',
  },

  interactiveMessage: {
    settings: { nodeName: getModuleText('interactiveMessage') },
    process: {
      messageContent: '',
      channel: '',
      interactiveType: '',
      options: [],
    },
    output: defaultOutput,
    input: {},
    type: 'interactiveMessage',
    typeId: '1.9',
    coordinates: defaultCoordinates(
      'Interactive Message',
      'Interactive Message',
      'interactiveMessage'
    ),
  },
  feedback: {
    settings: { nodeName: getModuleText('feedback') },
    process: { question: '', responseType: '', options: [] },
    output: defaultOutput,
    input: {},
    type: 'feedback',
    typeId: '1.10',
    coordinates: defaultCoordinates('Feedback', 'Feedback', 'feedback'),
  },
  notification: {
    settings: { nodeName: getModuleText('notification') },
    process: { notificationContent: '', channel: '', priority: '' },
    output: defaultOutput,
    input: {},
    type: 'notification',
    typeId: '1.11',
    coordinates: defaultCoordinates('Notification', 'Notification', 'notification'),
  },
};

export const getJsonConstant = (type: string) => {
  if (type in leapJsonConstant) {
    const json = leapJsonConstant[type as keyof typeof leapJsonConstant];
    return JSON.parse(JSON.stringify(json));
  }

  return getDefaultJson(type);
};

const getDefaultJson = (type: string) => {
  const label = getModuleText(type);
  return {
    settings: { timeout: 10000, title: label, nodeName: label },
    process: {
      URL: 'www.google.com',
      requestType: 'GET',
      headers: [
        { headerKey: 'type', headerValue: 'application/json' },
        { headerKey: 'language', headerValue: 'en' },
      ],
      requestBody: 'NA',
    },
    output: defaultOutput,
    input: {},
    type,
    typeId: '1.2',
    coordinates: defaultCoordinates(label, label, type),
  };
};
