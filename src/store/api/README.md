# Optimized API Tag System

This implementation provides an optimized tag system for RTK Query that handles:

- **Granular tagging**: Individual items are tagged by ID for precise cache invalidation
- **Optimistic updates**: UI updates immediately while API calls are in progress
- **Infinite scrolling**: Proper pagination data merging for seamless scrolling
- **Selective invalidation**: Only invalidates necessary cache entries

## Key Features

### 1. Granular Tagging
Instead of invalidating entire lists, we tag individual items:
```typescript
// Old approach - invalidates everything
invalidatesTags: ['FaqCategory']

// New approach - only invalidates specific item and related lists
invalidatesTags: (result, error, { id }) => faqCategoryTags.invalidatesItem(id)
```

### 2. Optimistic Updates
Updates happen immediately in the UI:
```typescript
async onQueryStarted({ id, ...patch }, { dispatch, queryFulfilled }) {
  // Update cache immediately
  const patchResult = dispatch(
    faqApi.util.updateQueryData('getFaqCategory', { id }, (draft) => {
      if (draft.data) {
        Object.assign(draft.data, patch);
      }
    })
  );
  
  try {
    await queryFulfilled;
    // Success - update propagated to all caches
  } catch {
    patchResult.undo(); // Rollback on failure
  }
}
```

### 3. Infinite Scroll Support
Automatic data merging for pagination:
```typescript
merge: (currentCache, newItems, { arg }) => {
  if (arg.page && arg.page > 1) {
    return infiniteScrollHelpers.mergePaginatedData(currentCache, newItems);
  }
  return newItems;
}
```

## Usage Examples

### Basic List with Optimistic Updates
```typescript
const { data, isLoading } = useGetFaqCategoriesQuery({ page: 1, limit: 20 });
const [updateCategory] = useUpdateFaqCategoryMutation();

const handleUpdate = async (id: string, updates: any) => {
  try {
    // UI updates immediately, API call happens in background
    await updateCategory({ id, ...updates }).unwrap();
  } catch (error) {
    // UI automatically reverts on error
    console.error('Update failed:', error);
  }
};
```

### Infinite Scroll Implementation
```typescript
const queryResult = useGetFaqCategoriesInfiniteQuery({ 
  page: 1, 
  limit: 20 
});

const { loadMoreRef, isLoadingMore } = useRTKInfiniteScroll(
  queryResult,
  faqApiHelpers.hasMoreFaqCategories,
  faqApiHelpers.getNextFaqCategoriesPage,
  queryResult.refetch,
  { page: 1, limit: 20 }
);

// Attach loadMoreRef to trigger element
<div ref={loadMoreRef}>Load more trigger</div>
```

### Single Item Updates
When updating a single item:
1. Cache for that specific item updates immediately
2. All list caches containing that item are updated
3. Only the specific item tag is invalidated
4. No unnecessary refetches occur

### Create Operations
When creating new items:
1. New item is optimistically added to the first page of lists
2. Only list tags are invalidated
3. Pagination counts are updated automatically

### Delete Operations
When deleting items:
1. Item is immediately removed from all list caches
2. Pagination counts are decremented
3. Both item and list tags are invalidated

## API Structure

Each optimized API follows this pattern:

```typescript
// Regular query for standard pagination
getItems: builder.query<Response, Params>({
  // ... standard implementation with merge support
})

// Infinite scroll version
getItemsInfinite: builder.query<Response, Params>({
  // ... infinite scroll optimized implementation
})

// Mutations with optimistic updates
updateItem: builder.mutation<Response, UpdateParams>({
  // ... optimistic update implementation
})
```

## Benefits

1. **Performance**: Only necessary cache updates, no full refetches
2. **User Experience**: Immediate UI feedback with automatic rollback on errors
3. **Scalability**: Handles large datasets with infinite scrolling
4. **Reliability**: Automatic error handling and cache consistency
5. **Developer Experience**: Simple hooks with built-in optimizations

## Migration Guide

To migrate existing APIs:

1. Import optimized utilities:
```typescript
import { createOptimizedTags, createCacheUpdaters, createInfiniteScrollHelpers } from '../../lib/utils/optimizedTags';
```

2. Create tag configurations:
```typescript
const entityTags = createOptimizedTags({
  entityType: 'EntityName',
  listTag: 'EntityNameList',
  itemTag: (id: string) => `EntityName-${id}`
});
```

3. Update query providers:
```typescript
providesTags: entityTags.providesList, // for lists
providesTags: entityTags.providesItem,  // for single items
```

4. Update mutation invalidators:
```typescript
invalidatesTags: (result, error, { id }) => entityTags.invalidatesItem(id),
```

5. Add optimistic updates to mutations using `onQueryStarted`

6. Add infinite scroll versions of list queries with merge logic

This system ensures optimal performance while maintaining data consistency and providing excellent user experience.