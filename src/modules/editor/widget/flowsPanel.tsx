import React, { useState, useEffect, ChangeEvent, KeyboardEvent } from 'react';
import {
  Plus,
  Search,
  ChevronsLeft,
  ChevronsRight,
  MoreVertical,
  X,
  Copy,
  FileClock,
  Trash,
} from 'lucide-react';
import { Flow } from '../types';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';
import {
  useCreateFlowMutation,
  useDeleteFlowMutation,
  useCloneFlowMutation,
  useGetFlowsQuery,
  useUpdateFlowMutation,
} from '@/store/api/flowApi';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { flowType } from '../types/enums/flow.enum';

interface FlowsPanelProps {
  onFlowSelect?: (flow: Flow) => void;
}
const FlowsPanel: React.FC<FlowsPanelProps> = ({ onFlowSelect }) => {
  const { botId } = useBotIdParam();
  const {
    data: flowsFromApi,
    isLoading,
    error,
    refetch,
  } = useGetFlowsQuery({ botId }, { skip: !botId });

  const [createFlow] = useCreateFlowMutation();
  const [deleteFlow] = useDeleteFlowMutation();
  const [cloneFlow] = useCloneFlowMutation();
  const [updateFlow] = useUpdateFlowMutation();

  const { t } = useTranslation();

  const [flows, setFlows] = useState<Flow[]>([]);
  const [activeFlowId, setActiveFlowId] = useState<string | null>(null);
  const [editingFlowId, setEditingFlowId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    if (flowsFromApi?.length > 0) {
      setFlows(flowsFromApi);
      setActiveFlowId(prev => prev ?? flowsFromApi[0].id);
      onFlowSelect?.(flowsFromApi[0]);
    }
  }, [flowsFromApi]);

  useEffect(() => {
    if (flowsFromApi?.length === 0 && !error && !isLoading) {
      handleCreateFlow();
    }
  }, [flowsFromApi]);

  const handleEditFlow = (flowId: string, currentName: string) => {
    setEditingFlowId(flowId);
    setEditingName(currentName);
  };

  const handleSaveEdit = async () => {
    if (!editingFlowId || !botId) return;
    const flow = flows.find(f => f.id === editingFlowId);
    if (!flow) return;

    const updatedName = editingName.trim() || flow.name;
    try {
      await updateFlow({
        appId: botId,
        flowId: editingFlowId,
        payload: { ...flow, name: updatedName, metadata: {} },
      }).unwrap();
      setEditingFlowId(null);
      setEditingName('');
      refetch();
    } catch (err) {
      console.error('Update flow failed:', err);
    }
  };

  const handleDeleteFlow = async (flowId: string, appId: string, type: string) => {
    if (type === flowType.DEFAULT || !botId) return;

    try {
      await deleteFlow({ appId, flowId }).unwrap();
      refetch();
      if (activeFlowId === flowId) {
        const remainingFlow = flows.find(f => f.id !== flowId);
        setActiveFlowId(remainingFlow?.id ?? null);
      }
    } catch (err) {
      console.error('Failed to delete flow:', err);
    }
  };

  const handleDuplicateFlow = async (flowId: string) => {
    try {
      await cloneFlow({ flowId }).unwrap();
    } catch (err) {
      console.error('Failed to duplicate flow:', err);
    }
  };

  const handleCreateFlow = async () => {
    if (!botId) return;

    const payload: Flow = {
      botId,
    };

    try {
      const res = await createFlow({ appId: botId, payload }).unwrap();

      refetch();
      setActiveFlowId(res.id);
    } catch (err) {
      console.error('Failed to create flow:', err);
    }
  };
  const handleSelectFlow = (flow: Flow) => {
    setActiveFlowId(flow.id);
    if (onFlowSelect) {
      onFlowSelect(flow);
    }
  };
  const filteredFlows = flows.filter(flow =>
    flow.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  return (
    <div
      className={cn(
        'absolute left-4 top-3 max-h-[60vh] bg-white rounded-lg shadow-floating flex flex-col overflow-hidden z-10 transition-all duration-300',
        isCollapsed ? 'w-22' : 'w-52'
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-secondary-200">
        {isCollapsed ? (
          <div className="flex justify-between items-center">
            <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
            <button onClick={() => setIsCollapsed(false)} aria-label="expand-panel">
              <ChevronsRight className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
            </button>
          </div>
        ) : isSearchActive ? (
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
              placeholder={t('common.searchFlows')}
              className="w-full text-sm text-secondary-900 border border-secondary-300 rounded-md px-3 py-2 pr-10"
              autoFocus
            />
            <button
              onClick={() => setIsSearchActive(false)}
              aria-label="close-search"
              className="absolute right-2 top-2"
            >
              <X className="w-5 h-5 text-black" />
            </button>
          </div>
        ) : (
          <div className="flex justify-between items-center">
            <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
            <div className="flex items-center space-x-2 p-1">
              <button
                onClick={handleCreateFlow}
                data-testid="add-flow-btn"
                className="w-6 flex justify-center items-center h-6 bg-primary-500 rounded-md"
              >
                <Plus className="w-4 h-4 text-white" />
              </button>
              <button onClick={() => setIsSearchActive(true)} data-testid="search-btn">
                <Search className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
              </button>
              <button onClick={() => setIsCollapsed(true)} aria-label="collapse-panel">
                <ChevronsLeft className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Flow List */}
      {!isCollapsed && (
        <div className="overflow-y-auto p-2 flex-1">
          {isLoading ? (
            <div className="text-center text-secondary-600">Loading...</div>
          ) : error ? (
            <div className="text-center text-error-400">Error loading flows</div>
          ) : (
            filteredFlows.map(flow => (
              <div key={flow.id} className="z-10">
                <div
                  className={cn(
                    'flex items-center my-0.5 justify-between rounded-md transition-colors cursor-pointer',
                    activeFlowId === flow.id ? 'bg-tertiary/10' : 'hover:bg-secondary-50',
                    editingFlowId === flow.id ? 'p-0' : 'px-1 h-11'
                  )}
                  onClick={() => handleSelectFlow(flow)}
                  onDoubleClick={() =>
                    flow.type !== flowType.DEFAULT && handleEditFlow(flow.id, flow.name)
                  }
                >
                  <div className="flex-1 relative">
                    {editingFlowId === flow.id ? (
                      <>
                        <input
                          value={editingName}
                          onChange={(e: ChangeEvent<HTMLInputElement>) =>
                            setEditingName(e.target.value)
                          }
                          className="font-medium text-secondary-900 text-sm border border-primary-500 rounded pl-2 pr-9 py-3 w-full"
                          onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') setEditingFlowId(null);
                          }}
                          onBlur={handleSaveEdit}
                          autoFocus
                        />
                        {editingName && (
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
                            onMouseDown={e => {
                              e.preventDefault();
                              setEditingName('');
                            }}
                          >
                            <X className="pr-1 w-7 h-7" />
                          </button>
                        )}
                      </>
                    ) : (
                      <div className="flex justify-start gap-2 items-center">
                        <div
                          className={cn(
                            'text-xs text-tertiary-600 truncate w-auto max-w-24',
                            activeFlowId === flow.id ? 'font-medium' : 'opacity-50'
                          )}
                          title={flow.name}
                        >
                          {flow.name}
                        </div>
                        {flow.type === flowType.DEFAULT && (
                          <div className="text-xs bg-primary-400/10 px-2 py-1 rounded-md text-primary-400">
                            {flow.type}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {editingFlowId !== flow.id && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button data-testid="more-options-btn">
                          <MoreVertical className="w-4 h-4 text-tertiary-600" />
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="-top-20 w-48 z-50">
                        <DropdownMenuItem
                          onClick={() => handleDuplicateFlow(flow.id)}
                          className="flex items-center gap-2"
                        >
                          <Copy className="w-4 h-4" />
                          <span>{t('common.duplicate')}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="flex items-center gap-2">
                          <FileClock className="w-4 h-4" />
                          <span>{t('common.versionHistory')}</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          aria-label="delete-flow-btn"
                          onClick={() => handleDeleteFlow(flow.id, flow.appId, flow.type)}
                          className={cn(
                            'flex items-center gap-2',
                            flow.type === flowType.DEFAULT
                              ? 'text-error-200 cursor-not-allowed'
                              : 'text-error-400 hover:bg-error-50'
                          )}
                          disabled={flow.type === flowType.DEFAULT}
                        >
                          <Trash className="w-4 h-4" />
                          <span>{t('common.delete')}</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default FlowsPanel;
