import { FilterType, ChannelId, ChannelMainTab } from './enums';
import { Channel, ChannelFilter } from './types';
import { TabConfig } from '@/types';

import WhatsAppIcon from '@/assets/icons/WhatsApp.png';
import WebhookIcon from '@/assets/icons/WebhookNative.png';
import FacebookIcon from '@/assets/icons/facebook-messenger.png';
import InstagramIcon from '@/assets/icons/Instagram.png';
import TelegramIcon from '@/assets/icons/telegram.png';
import AmazonAlexaIcon from '@/assets/icons/Amazon_Alexa_blue_logo.png';

export const availableChannels: Channel[] = [
  {
    id: ChannelId.Webhook,
    name: 'Webhook',
    type: 'native',
    status: 'available',
    icon: WebhookIcon,
  },
  {
    id: ChannelId.FacebookMessenger,
    name: 'Facebook Messenger',
    type: 'text',
    status: 'available',
    icon: FacebookIcon,
    isVerified: true,
  },
  {
    id: ChannelId.Instagram,
    name: 'Instagram',
    type: 'text',
    status: 'available',
    icon: InstagramIcon,
  },
  {
    id: ChannelId.WhatsApp,
    name: 'WhatsApp',
    type: 'text',
    status: 'available',
    icon: WhatsAppIcon,
  },
  {
    id: ChannelId.Telegram,
    name: 'Telegram',
    type: 'text',
    status: 'available',
    icon: TelegramIcon,
  },
  {
    id: ChannelId.Alexa,
    name: 'Alexa',
    type: 'voice',
    status: 'available',
    icon: AmazonAlexaIcon,
  },
];

export const myChannels: Channel[] = [
  {
    id: ChannelId.WhatsApp,
    name: 'WhatsApp',
    type: 'text',
    status: 'connected',
    icon: WhatsAppIcon,
    phoneNumber: '+91 9642456783',
    webhookUrl: 'https://ngage.cpaas.com/v1/bot/wellness_care',
    isVerified: true,
  },
];

export const filters: ChannelFilter[] = [
  { id: 'all', labelKey: 'channels.filters.all', type: FilterType.ALL },
  { id: 'native', labelKey: 'channels.filters.native', type: FilterType.NATIVE },
  { id: 'text', labelKey: 'channels.filters.text', type: FilterType.TEXT },
  { id: 'voice', labelKey: 'channels.filters.voice', type: FilterType.VOICE },
];

export const mainChannelTab: TabConfig[] = [
  {
    id: ChannelMainTab.AVAILABLE,
    labelKey: 'channels.tabs.available',
  },
  {
    id: ChannelMainTab.MY_CHANNELS,
    labelKey: 'channels.tabs.myChannels',
  },
];
