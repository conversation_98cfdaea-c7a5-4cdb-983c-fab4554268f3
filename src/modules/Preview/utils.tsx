import { DatePicker } from "@/components/DatePicker/date-picker";
import TimePicker from "@/components/time-picker";
import { Input } from "@/components/ui/input";
import { TimeFormat } from "@/types/enums/enums";

export const FIELD_RENDERERS = {
  time: (props) => (
    <TimePicker
      disabled={props.formLocked}
      value={props.value}
      onChange={props.onChange}
      className={props.className}
      format={TimeFormat.TWELVE_HOUR}
    />
  ),
  date: (props) => (
    <DatePicker
      disabled={props.formLocked}
      selected={props.selected}
      onSelect={props.onSelect}
      fieldType={props.fieldType}
      customRange={props.customRange}
      className={props.className}
    />
  ),
  email: (props) => (
    <Input
      type="email"
      name={props.fieldName}
      placeholder={props.placeholder}
      value={props.value}
      onChange={props.onChange}
      disabled={props.formLocked}
      className={props.className}
    />
  ),
  text: (props) => (
    <Input
      type="text"
      name={props.fieldName}
      placeholder={props.placeholder}
      value={props.value}
      onChange={props.onChange}
      disabled={props.formLocked}
      className={props.className}
    />
  ),
  // ...add more types as needed
};
