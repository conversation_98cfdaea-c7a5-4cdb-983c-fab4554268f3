import React, { useCallback } from 'react';
import { cn } from '@/lib/utils';
import { getModuleIcon, getModuleText } from '@/modules/editor/utils/config';
import { StencilNodesType } from '../../utils/constants';

interface NodeItemProps {
  type: StencilNodesType; // Use StencilNodesType for stricter typing
  startDrag: (event: React.PointerEvent, type: string) => void;
  isComingSoon?: boolean;
}

const NodeItem: React.FC<NodeItemProps> = ({ type, startDrag, isComingSoon }) => {
  const handlePointerDown = useCallback(
    (event: React.PointerEvent) => {
      if (isComingSoon) return;
      event.preventDefault();
      startDrag(event, type);
    },
    [startDrag, type, isComingSoon]
  );

  return (
    <div
      className={cn(
        'rounded-lg hover:border-primary-300 transition-all cursor-pointer bg-white flex flex-col gap-3 w-12',
        { 'opacity-50 select-none cursor-not-allowed': isComingSoon }
      )}
      onPointerDown={handlePointerDown}
    >
      <div className="rounded-2xl flex items-center justify-center bg-primary-500/5 p-3 text-primary-600 flex-shrink-0 stencil-node">
        {typeof getModuleIcon(type) === 'string' ? (
          <img
            src={getModuleIcon(type) as string}
            className="text-primary-500 !w-6 !h-6"
            alt={type}
          />
        ) : (
          <div className=" border py-3 px-3  text-primary-500 rounded-2xl">
            {React.createElement(getModuleIcon(type) as React.ElementType, {
              size: 20,
            })}
          </div>
        )}
      </div>

      <p className="text-xs text-center text-wrap text-primary-600 w-full">{getModuleText(type)}</p>
    </div>
  );
};

export default NodeItem;
