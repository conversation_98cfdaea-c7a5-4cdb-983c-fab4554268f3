"use strict";
// lib/utils/optimizedTags.ts
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createEntityMixins = exports.defaultInfiniteSerializeArgs = exports.defaultForceRefetch = exports.defaultPaginatedMerge = exports.defaultSerializeListArgs = exports.createInfiniteScrollHelpers = exports.createCacheUpdaters = exports.createOptimizedTags = void 0;
// import { ApiResponse, PaginatedResponse, OrderDirection, OrderArray } from '@/types';
var types_1 = require("../../types");
var createOptimizedTags = function (config) {
    var entityType = config.entityType;
    var listTag = "".concat(entityType, "List");
    var createListTag = function () { return ({ type: listTag, id: 'LIST' }); };
    var createEntityTag = function (id) { return ({ type: entityType, id: id }); };
    return {
        providesList: function () { return [createListTag()]; },
        providesItem: function (_, __, arg) { return [
            createEntityTag(arg.id),
        ]; },
        invalidatesItem: function (id) { return [createEntityTag(id)]; },
        invalidatesList: function () { return [createListTag()]; },
        invalidatesAll: function () { return [{ type: entityType }, { type: listTag }]; },
    };
};
exports.createOptimizedTags = createOptimizedTags;
// ------------------ Sorting Helper ------------------
var compareWithOrder = function (a, b, orderBy) {
    for (var _i = 0, orderBy_1 = orderBy; _i < orderBy_1.length; _i++) {
        var _a = orderBy_1[_i], field = _a[0], direction = _a[1];
        var aVal = a[field];
        var bVal = b[field];
        if (aVal < bVal)
            return direction === types_1.OrderDirection.ASC ? -1 : 1;
        if (aVal > bVal)
            return direction === types_1.OrderDirection.ASC ? 1 : -1;
    }
    return 0;
};
// ------------------ Cache Updaters ------------------
var createCacheUpdaters = function () { return ({
    updateItemInLists: function (draft, updatedItem, orderBy) {
        var _a;
        if (!((_a = draft === null || draft === void 0 ? void 0 : draft.data) === null || _a === void 0 ? void 0 : _a.items))
            return;
        var index = draft.data.items.findIndex(function (i) { return i.id === updatedItem.id; });
        if (index === -1)
            return;
        draft.data.items.splice(index, 1, updatedItem);
        if (orderBy && orderBy.length > 0) {
            draft.data.items.sort(function (a, b) { return compareWithOrder(a, b, orderBy); });
        }
    },
    removeItemFromLists: function (draft, itemId) {
        var _a;
        if (!((_a = draft === null || draft === void 0 ? void 0 : draft.data) === null || _a === void 0 ? void 0 : _a.items))
            return;
        draft.data.items = draft.data.items.filter(function (i) { return i.id !== itemId; });
        if (draft.data.pagination) {
            draft.data.pagination.total = Math.max(0, draft.data.pagination.total - 1);
        }
    },
    addItemToLists: function (draft, newItem, orderBy) {
        var _a;
        if (!((_a = draft === null || draft === void 0 ? void 0 : draft.data) === null || _a === void 0 ? void 0 : _a.items))
            return;
        var exists = draft.data.items.some(function (i) { return i.id === newItem.id; });
        if (exists)
            return;
        draft.data.items.push(newItem);
        if (orderBy && orderBy.length > 0) {
            draft.data.items.sort(function (a, b) { return compareWithOrder(a, b, orderBy); });
        }
        else {
            draft.data.items.unshift(newItem);
        }
        if (draft.data.pagination) {
            draft.data.pagination.total += 1;
        }
    },
}); };
exports.createCacheUpdaters = createCacheUpdaters;
// ------------------ Infinite Scroll Helpers ------------------
var createInfiniteScrollHelpers = function () { return ({
    mergePaginatedData: function (existing, incoming) {
        var _a;
        if (!(existing === null || existing === void 0 ? void 0 : existing.data))
            return incoming;
        var existingIds = new Set((existing.data.items || []).map(function (i) { return i.id; }));
        var newItems = (((_a = incoming.data) === null || _a === void 0 ? void 0 : _a.items) || []).filter(function (i) { return !existingIds.has(i.id); });
        return __assign(__assign({}, incoming), { data: __assign(__assign({}, incoming.data), { items: __spreadArray(__spreadArray([], (existing.data.items || []), true), newItems, true), pagination: incoming.data.pagination }) });
    },
    hasMorePages: function (data) { var _a, _b; return ((_b = (_a = data === null || data === void 0 ? void 0 : data.data) === null || _a === void 0 ? void 0 : _a.pagination) === null || _b === void 0 ? void 0 : _b.hasNext) || false; },
    getNextPage: function (data) { var _a, _b; return (((_b = (_a = data === null || data === void 0 ? void 0 : data.data) === null || _a === void 0 ? void 0 : _a.pagination) === null || _b === void 0 ? void 0 : _b.page) || 1) + 1; },
}); };
exports.createInfiniteScrollHelpers = createInfiniteScrollHelpers;
// ------------------ Serialize & Merge Helpers ------------------
var defaultSerializeListArgs = function (_a) {
    var queryArgs = _a.queryArgs, endpointName = _a.endpointName;
    var _b = queryArgs, query = _b.query, baseArgs = __rest(_b, ["query"]);
    return "".concat(endpointName, "-").concat(JSON.stringify(baseArgs));
};
exports.defaultSerializeListArgs = defaultSerializeListArgs;
var defaultPaginatedMerge = function (currentCache, newItems, _a) {
    var _b;
    var arg = _a.arg;
    if (((_b = arg === null || arg === void 0 ? void 0 : arg.query) === null || _b === void 0 ? void 0 : _b.page) > 1) {
        return (0, exports.createInfiniteScrollHelpers)().mergePaginatedData(currentCache, newItems);
    }
    return newItems;
};
exports.defaultPaginatedMerge = defaultPaginatedMerge;
var defaultForceRefetch = function (_a) {
    var currentArg = _a.currentArg, previousArg = _a.previousArg;
    return JSON.stringify(currentArg || {}) !== JSON.stringify(previousArg || {});
};
exports.defaultForceRefetch = defaultForceRefetch;
var defaultInfiniteSerializeArgs = function (_a) {
    var queryArgs = _a.queryArgs, endpointName = _a.endpointName;
    return (__assign(__assign({ endpoint: endpointName }, queryArgs), { infinite: true }));
};
exports.defaultInfiniteSerializeArgs = defaultInfiniteSerializeArgs;
var createEntityMixins = function (config) {
    var entityType = config.entityType, apiUtil = config.apiUtil, getListCacheKey = config.getListCacheKey;
    var tags = (0, exports.createOptimizedTags)({ entityType: entityType });
    var updaters = (0, exports.createCacheUpdaters)();
    var infiniteScroll = (0, exports.createInfiniteScrollHelpers)();
    var resolveOrderBy = function (cacheKey) {
        var _a;
        var order = (cacheKey === null || cacheKey === void 0 ? void 0 : cacheKey.order) || ((_a = cacheKey === null || cacheKey === void 0 ? void 0 : cacheKey.query) === null || _a === void 0 ? void 0 : _a.order);
        return Array.isArray(order) && order.length > 0 ? order : undefined;
    };
    return {
        paginated: {
            providesTags: tags.providesList,
            serializeQueryArgs: exports.defaultSerializeListArgs,
            merge: exports.defaultPaginatedMerge,
            forceRefetch: exports.defaultForceRefetch,
        },
        infinite: {
            providesTags: tags.providesList,
            serializeQueryArgs: exports.defaultInfiniteSerializeArgs,
            merge: infiniteScroll.mergePaginatedData,
            forceRefetch: exports.defaultForceRefetch,
        },
        item: { providesTags: tags.providesItem },
        create: {
            onQueryStarted: function (arg_1, _a) {
                return __awaiter(this, arguments, void 0, function (arg, _b) {
                    var data_1, state, api_1, _c;
                    var dispatch = _b.dispatch, queryFulfilled = _b.queryFulfilled, getState = _b.getState;
                    return __generator(this, function (_d) {
                        switch (_d.label) {
                            case 0:
                                _d.trys.push([0, 2, , 3]);
                                return [4 /*yield*/, queryFulfilled];
                            case 1:
                                data_1 = (_d.sent()).data;
                                if (data_1.success && data_1.data) {
                                    state = getState();
                                    api_1 = state.api;
                                    Object.keys(api_1.queries).forEach(function (key) {
                                        var query = api_1.queries[key];
                                        if ((query === null || query === void 0 ? void 0 : query.endpointName) === "get".concat(entityType, "s")) {
                                            var cacheKey_1 = query.originalArgs;
                                            dispatch(apiUtil.updateQueryData("get".concat(entityType, "s"), cacheKey_1, function (draft) {
                                                updaters.addItemToLists(draft, data_1.data, resolveOrderBy(cacheKey_1));
                                            }));
                                        }
                                    });
                                }
                                return [3 /*break*/, 3];
                            case 2:
                                _c = _d.sent();
                                return [3 /*break*/, 3];
                            case 3: return [2 /*return*/];
                        }
                    });
                });
            },
        },
        update: {
            invalidatesTags: function (_, __, _a) {
                var id = _a.id;
                return tags.invalidatesItem(id);
            },
            onQueryStarted: function (_a, _b) {
                return __awaiter(this, void 0, void 0, function () {
                    var patchResult, data_2, state, api_2, _c;
                    var id = _a.id, patch = __rest(_a, ["id"]);
                    var dispatch = _b.dispatch, queryFulfilled = _b.queryFulfilled, getState = _b.getState;
                    return __generator(this, function (_d) {
                        switch (_d.label) {
                            case 0:
                                patchResult = dispatch(apiUtil.updateQueryData("get".concat(entityType), { id: id }, function (draft) {
                                    if (draft.data)
                                        Object.assign(draft.data, patch);
                                }));
                                _d.label = 1;
                            case 1:
                                _d.trys.push([1, 3, , 4]);
                                return [4 /*yield*/, queryFulfilled];
                            case 2:
                                data_2 = (_d.sent()).data;
                                if (data_2.success && data_2.data) {
                                    state = getState();
                                    api_2 = state.api;
                                    Object.keys(api_2.queries).forEach(function (key) {
                                        var query = api_2.queries[key];
                                        if ((query === null || query === void 0 ? void 0 : query.endpointName) === "get".concat(entityType, "s")) {
                                            var cacheKey_2 = query.originalArgs;
                                            dispatch(apiUtil.updateQueryData("get".concat(entityType, "s"), cacheKey_2, function (draft) {
                                                updaters.updateItemInLists(draft, data_2.data, resolveOrderBy(cacheKey_2));
                                            }));
                                        }
                                    });
                                }
                                return [3 /*break*/, 4];
                            case 3:
                                _c = _d.sent();
                                patchResult.undo();
                                return [3 /*break*/, 4];
                            case 4: return [2 /*return*/];
                        }
                    });
                });
            },
        },
        delete: {
            invalidatesTags: function (_, __, _a) {
                var id = _a.id;
                return tags.invalidatesItem(id);
            },
            onQueryStarted: function (_a, _b) {
                return __awaiter(this, arguments, void 0, function (_c, _d) {
                    var patchResults, state, api_3, _e;
                    var id = _c.id;
                    var dispatch = _d.dispatch, queryFulfilled = _d.queryFulfilled, getState = _d.getState;
                    return __generator(this, function (_f) {
                        switch (_f.label) {
                            case 0:
                                patchResults = [];
                                _f.label = 1;
                            case 1:
                                _f.trys.push([1, 3, , 4]);
                                state = getState();
                                api_3 = state.api;
                                Object.keys(api_3.queries).forEach(function (key) {
                                    var query = api_3.queries[key];
                                    if ((query === null || query === void 0 ? void 0 : query.endpointName) === "get".concat(entityType, "s")) {
                                        var cacheKey = query.originalArgs;
                                        var patchResult = dispatch(apiUtil.updateQueryData("get".concat(entityType, "s"), cacheKey, function (draft) {
                                            updaters.removeItemFromLists(draft, id);
                                        }));
                                        patchResults.push(patchResult);
                                    }
                                });
                                return [4 /*yield*/, queryFulfilled];
                            case 2:
                                _f.sent();
                                return [3 /*break*/, 4];
                            case 3:
                                _e = _f.sent();
                                patchResults.forEach(function (r) { return r.undo(); });
                                return [3 /*break*/, 4];
                            case 4: return [2 /*return*/];
                        }
                    });
                });
            },
        },
        tags: tags,
        infiniteScrollHelpers: infiniteScroll,
    };
};
exports.createEntityMixins = createEntityMixins;
