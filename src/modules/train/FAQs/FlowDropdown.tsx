import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FloatingField } from '@/components/ui/floating-label';
import { useGetFlowsQuery } from '@/store/api/flowApi';
import { useBotIdParam } from '@/hooks/useRouterParam';

interface FlowDropdownProps {
  name: string;
  initialValue?: string;
  onChange: (value: string) => void;
}

const FlowDropdown: React.FC<FlowDropdownProps> = ({ name, initialValue, onChange }) => {
  const { t } = useTranslation();
  const { watch } = useFormContext();
  const { botId } = useBotIdParam();

  const { data: flowsData, isLoading, isError } = useGetFlowsQuery({ botId });

  const flows =
    flowsData?.map(flow => ({
      label: flow.name,
      value: flow.id,
    })) || [];

  const selectedFlowId = watch(name) || initialValue;

  if (isLoading) {
    return <div>{t('common.loading')}</div>; // Or a skeleton loader
  }

  if (isError) {
    return <div>{t('common.error')}</div>; // Or an error message
  }

  return (
    <FloatingField
      as="select"
      label={t('faqs.items.chooseFlowPlaceholder')}
      onChange={onChange}
      value={selectedFlowId}
      options={flows}
      className="data-[placeholder]:text-tertiary-500 w-56"
    />
  );
};

export default FlowDropdown;
