import React, { useState } from 'react';
import { Import, SquareArrowOutUpRight } from 'lucide-react';
import AddModal from '../components/AddModal';
import { Button } from '@/components/ui/button';
import AddQuestionForm from './AddQuestionForm';
import { useTranslation } from 'react-i18next';
import { FaqCategory } from '@/types';
import FaqList from './FaqList';
import LanguageDropdown from '@/components/LanguageDropdown';

interface RightPanelContentProps {
  selectedCategory: FaqCategory;
}

const RightPanelContent: React.FC<RightPanelContentProps> = ({ selectedCategory }) => {
  const [isAddQuestionModalOpen, setIsAddQuestionModalOpen] = useState(false);
  const { t } = useTranslation();
  const [language, setLanguage] = useState('');

  const AddQuestionModal = (
    <AddModal
      title={t('faqs.items.addTitle')}
      open={isAddQuestionModalOpen}
      onOpenChange={setIsAddQuestionModalOpen}
      className="sm:max-w-[850px]"
      trigger={
        <Button variant="outline" onClick={() => setIsAddQuestionModalOpen(true)}>
          {t('faqs.items.addTitle')}
        </Button>
      }
    >
      <AddQuestionForm
        categoryId={selectedCategory.id}
        botId={selectedCategory.botId}
        onClose={() => setIsAddQuestionModalOpen(false)}
        selectedLangId={language}
      />
    </AddModal>
  );

  return (
    <div className="rounded-lg shadow-sm flex-1 h-0 flex flex-col">
      <div className="flex justify-between space-x-4 items-center mb-6">
        <LanguageDropdown onChange={setLanguage} />

        <div className="flex mb-4 gap-7 items-center">
          <div className="flex gap-4 items-end">
            <Import
              title="Import"
              className="w-6 h-6 text-tertiary-600 cursor-not-allowed"
              data-testid="download-icon"
            />
            <SquareArrowOutUpRight
              title="Share"
              className="w-6 h-6 text-tertiary-600 cursor-not-allowed"
              data-testid="share-icon"
            />
          </div>
          {AddQuestionModal}
        </div>
      </div>

      <FaqList
        selectedCategory={selectedCategory}
        language={language}
        AddQuestionModal={AddQuestionModal}
      />
    </div>
  );
};

export default RightPanelContent;
