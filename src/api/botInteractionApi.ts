import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// --- Re-defining types based on bot-interaction-service for frontend consumption ---

// From src/schemas/conversation.schemas.ts
export enum ChannelType {
  WEB = "web",
  WHATSAPP = "whatsapp",
}

interface Entity {
  confidence_entity: number;
  end: number;
  entity: string;
  extractor: string;
  processors: string[];
  start: number;
  value: string;
}

interface NLUResponse {
  intent: {
    name: string;
    confidence: number;
  };
  entities: Entity[];
  text: string;
  conversationId?: string;
}

export interface SendMessageRequest {
  botId: string; // Assuming UUID string
  content: string;
  messageType?: "text" | "image" | "file";
  channelType?: ChannelType;
  formData?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface SendMessageParam {
  id: string; // Conversation ID (UUID string)
}

// From src/types/message.types.ts
export interface OutgoingMessage {
  nodeType: string; // Simplified, could be more specific like FlowNode enum
  data: {
    text?: string;
    type?: string; // Simplified, could be MessageNodeType enum
    [key: string]: any;
  };
  // Add other properties if needed, e.g., conversationId, messageType
}

export interface SendMessageResponse {
  conversationId: string;
  response: OutgoingMessage[];
}

// From src/services/debugger.service.ts
export enum DebuggerEventType {
  LOG = "log",
  NLU_LOG = "nlu_log",
  CONTEXT = "context",
}

export interface LogPayload {
  level: "info" | "warning" | "error";
  message: string;
  details?: any;
}

export interface NluLogPayload {
  intent: {
    name: string;
    confidence: number;
  };
  entities: any[]; // Simplified, could be Entity[]
  text: string;
}

export interface ConversationContext {
  chatConversationId: string;
  userId?: string;
  botId: string;
  currentJourneyId?: string | null;
  invokedIntent: NLUResponse | null;
  waitingForInput?: boolean;
  asyncOperationInProgress?: boolean;
  sessionStartedAt: Date;
  lastActivityAt: Date;
  metadata: any; // Simplified
  expiresAt: Date;
  preservedContext?: any; // Simplified
  journeyContext: any; // Simplified
}

export interface DebuggerEvent {
  type: DebuggerEventType;
  timestamp: string;
  conversationId: string;
  payload: LogPayload | NluLogPayload | ConversationContext;
}

// --- RTK Query API Slice ---

export const botInteractionApi = createApi({
  reducerPath: "botInteractionApi",
  baseQuery: fetchBaseQuery({ baseUrl: "/api/v1/" }), // Assuming your frontend serves from root and proxies to /api/v1
  endpoints: (builder) => ({
    sendPreviewMessage: builder.mutation<
      SendMessageResponse,
      { id: string; body: SendMessageRequest }
    >({
      query: ({ id, body }) => ({
        url: `preview/conversations/${id}/message`,
        method: "POST",
        body,
      }),
    }),
    // Note: RTK Query is not ideal for Server-Sent Events (SSE) as it expects a single response.
    // For real-time streams like the debugger, you would typically use a separate SSE client
    // (e.g., EventSource API) on the frontend. This endpoint definition is for illustrative
    // purposes of how you might type the request, but the actual consumption would differ.
    getDebuggerStream: builder.query<DebuggerEvent, { conversationId: string }>({
      query: ({ conversationId }) => `debugger/stream/${conversationId}`,
      // Keepalive and streaming logic would be handled outside RTK Query
      // transformResponse: (response: DebuggerEvent) => response, // Example transform if needed
    }),
  }),
});

export const { useSendPreviewMessageMutation, useGetDebuggerStreamQuery } = botInteractionApi;