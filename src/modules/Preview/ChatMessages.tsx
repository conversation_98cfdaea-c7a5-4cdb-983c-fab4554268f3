'use client';

import React, { memo, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { RichTextEditor } from '@/modules/rich-text-editor';
import DynamicFormPrompt from './DynamicFormPrompt';
import { ChatMessage, BotFormFields } from './types/types';

interface ChatMessagesProps {
  messages: ChatMessage[];
  lastFormPrompt: ChatMessage | null;
  formLocked: boolean;
  onFormSubmit: (fields: BotFormFields) => Promise<void>;
  lastFormFieldValues?: BotFormFields;
  loading?: boolean;
  onFormActiveChange?: (active: boolean) => void;
}

const ChatMessages: React.FC<ChatMessagesProps> = memo(
  ({
    messages,
    lastFormPrompt,
    formLocked,
    onFormSubmit,
    lastFormFieldValues,
    loading,
    onFormActiveChange,
  }) => {
    const chatEndRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslation();

    const hasActiveForm = messages.some(msg => {
      if (msg.nodeType !== 'form' || !msg.data?.prompt?.length) return false;

      const promptFields = msg.data.prompt;
      const singleField = promptFields.length === 1 ? promptFields[0] : null;

      if (singleField?.fieldType === 'text') return false;

      return !msg.data?.submittedValues;
    });

    // ✅ Notify parent when active form state changes
    useEffect(() => {
      onFormActiveChange?.(hasActiveForm);
    }, [hasActiveForm, onFormActiveChange]);

    const contentExtractors: Record<string, (msg: ChatMessage) => string> = {
      user_message: msg => msg.data?.text ?? '',
      message: msg => msg.data?.text ?? msg.data?.content ?? '',
      form: msg => msg.data?.text ?? '',
    };
    return (
      <div className="flex-1 border-none p-4 space-y-3 overflow-y-auto">
        {messages.map((msg, idx) => {
          if (msg.nodeType === 'form' && msg.data?.prompt?.length) {
            //TODO: change this to nodeConfigSheet file type
            const isLocked = !!msg.data?.submittedValues || (lastFormPrompt !== msg && formLocked);
            const promptFields = msg.data.prompt;
            const singleField = promptFields.length === 1 ? promptFields[0] : null;

            if (singleField && singleField.fieldType === 'text') {
              return null;
            }

            return (
              <div key={idx} className="flex justify-start">
                <div className="bg-transparent text-secondary-900 w-full max-w-[90%]">
                  <DynamicFormPrompt
                    message={msg}
                    formDisabled={isLocked}
                    onSubmit={onFormSubmit}
                    defaultValues={msg.data?.submittedValues ?? lastFormFieldValues}
                  />
                </div>
              </div>
            );
          }

          const content = contentExtractors[msg.nodeType]?.(msg) ?? '';
          if (!content) return null;
          return (
            <div
              key={idx}
              className={cn('flex', msg.sender === 'user' ? 'justify-end' : 'justify-start')}
            >
              <div
                className={cn(
                  'max-w-[80%] px-4 py-2 rounded-2xl shadow-sm text-base',
                  msg.sender === 'user'
                    ? 'bg-primary-100 text-primary-900 rounded-br-md'
                    : 'bg-tertiary-100 text-secondary-900 rounded-bl-md'
                )}
              >
                <RichTextEditor
                  content={content}
                  readOnly
                  isToolbar={false}
                  className="bg-transparent border-none shadow-none p-0"
                />
              </div>
            </div>
          );
        })}

        {loading && (
          <div className="flex justify-start">
            <div className="px-4 py-2 rounded-2xl bg-tertiary-100 text-secondary-400 text-base animate-pulse">
              {t('form.typing')}
            </div>
          </div>
        )}
        <div ref={chatEndRef} />
      </div>
    );
  }
);

export default ChatMessages;
