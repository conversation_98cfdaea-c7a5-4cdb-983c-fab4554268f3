import DropdownButton from '@/components/dropdownButton';
import { platformOptions, PlatformType } from '@/modules/editor/utils/constants';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import LanguageDropdown from '@/components/LanguageDropdown';
import { Globe } from 'lucide-react';

export default function ChannelLangDropdown() {
  const { control } = useFormContext();

  return (
    <>
      <div className="flex items-center px-4 pt-4 space-x-2">
        <FormField
          control={control}
          name="settings.language"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LanguageDropdown
                  onChange={(_, node) => field.onChange(node.name.toLowerCase())}
                  initialValue={field.value}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="settings.platform"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DropdownButton
                  value={field.value || PlatformType.Web}
                  onChange={field.onChange}
                  options={platformOptions}
                  icon={<Globe className="w-5 h-5 text-tertiary-600" />}
                  className="min-w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </>
  );
}
