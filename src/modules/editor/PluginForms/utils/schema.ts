import z from 'zod';
import { StencilNodesType } from '../../utils/constants';
import { FormField } from '@/modules/Preview/types';

export const appStart = z.object({
  process: z.object({
    intentId: z.string().min(1, 'Intent ID is required'),
  }),
});

export const form = z.object({
  process: z.object({
    formId: z.string().optional(),

    channelData: z
      .record(
        // platform (e.g., 'web', 'whatsapp')
        z.record(
          // language (e.g., 'english', 'hindi')
          z.object({
            formConfig: z
              .object({
                formType: z.string().optional(),
                fields: z
                  .array(
                    z.object({
                      name: z.string().optional(),
                      type: z.string().optional(),
                      label: z.string().optional(),
                      required: z.boolean().optional(),
                      options: z.array(z.string()).optional(),
                    })
                  )
                  .optional(),
              })
              .optional(),
          })
        )
      )
      .optional(),
  }),
});

export const message = z.object({
  process: z.object({
    channelData: z
      .record(
        // platform (e.g., 'web', 'whatsapp')
        z.record(
          // language (e.g., 'english', 'hindi')
          z.object({
            messageText: z.string().min(1, 'Message text is required'),

            images: z.array(z.string()).optional(),

            videos: z.array(z.string()).optional(),

            files: z.array(z.string()).optional(),
          })
        )
      )
      .optional(),
  }),
});

export const flowConnector = z.object({
  process: z.object({
    flowConfig: z.object({
      targetFlowId: z.string().min(1, 'Target Flow is required'),

      passExistingContext: z.boolean().optional(),

      resumeFromThisContext: z.boolean().optional(),

      journeyId: z.string(),
    }),
  }),
});

// Basic schema for other node types

const basicSchema = z.object({
  settings: z.object({
    nodeName: z.string().min(1, 'Node name is required'),
  }),

  process: z.object({}).passthrough(),
});

const schemaMap: Record<StencilNodesType, z.ZodTypeAny> = {
  appStart,

  form,

  message,

  appEnd: basicSchema,

  whatsapp: basicSchema,

  agentTransfer: basicSchema,

  http: basicSchema,

  choice: basicSchema,

  flowConnector: flowConnector,

  language: basicSchema,

  payment: basicSchema,

  script: basicSchema,

  waitDelay: basicSchema,

  interactiveMessage: basicSchema,

  feedback: basicSchema,

  notification: basicSchema,
};

export const getFormSchema = (type: StencilNodesType) => {
  return { schema: schemaMap[type] || basicSchema };
};

export const formSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required'),
});

export const createTextFieldSchema = (field: FormField) =>
  field.required
    ? z.string().min(1, `${field.label || field.fieldName} is required`)
    : z.string().optional();

export const createPastDateSchema = (field: FormField) =>
  field.required
    ? z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).refine(
        val => {
          const date = val instanceof Date ? val : new Date(val);
          return !isNaN(date.getTime()) && date < currentDate;
        },
        { message: `${field.label || field.fieldName} must be a past date` }
      )
    : z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).optional();

export const createCustomDateSchema = (field: FormField) =>
  field.required
    ? z
        .object({
          start: z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).refine(
            val => {
              const date = val instanceof Date ? val : new Date(val);
              return !isNaN(date.getTime());
            },
            { message: `${field.label || field.fieldName}.start must be a valid date` }
          ),
          end: z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).refine(
            val => {
              const date = val instanceof Date ? val : new Date(val);
              return !isNaN(date.getTime());
            },
            { message: `${field.label || field.fieldName}.end must be a valid date` }
          ),
        })
        .refine(
          data => {
            const start = data.start instanceof Date ? data.start : new Date(data.start);
            const end = data.end instanceof Date ? data.end : new Date(data.end);
            return start <= end;
          },
          { message: `${field.label || field.fieldName} end date must be after start date` }
        )
        .refine(
          data => {
            const start = data.start instanceof Date ? data.start : new Date(data.start);
            const end = data.end instanceof Date ? data.end : new Date(data.end);
            const rangeStart = field.rangeStart ? new Date(field.rangeStart) : new Date(0);
            const rangeEnd = field.rangeEnd ? new Date(field.rangeEnd) : new Date();
            return start >= rangeStart && end <= rangeEnd;
          },
          {
            message: `${field.label || field.fieldName} must be between ${field.rangeStart || 'start'} and ${field.rangeEnd || 'end'}`,
          }
        )
    : z
        .object({
          start: z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).optional(),
          end: z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).optional(),
        })
        .optional();
export const createTimeSchema = (field: FormField) =>
  field.required
    ? z
        .string()
        .min(1, `${field.label || field.fieldName} is required`)
        .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
          message: `${field.label || field.fieldName} must be a valid time (HH:MM)`,
        })
    : z
        .string()
        .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .optional();

export const createNumberSchema = (field: FormField) =>
  field.required
    ? z.number().min(0, `${field.label || field.fieldName} must be a positive number`)
    : z.number().min(0).optional();

export const createPasswordSchema = (field: FormField) =>
  field.required
    ? z.string().min(6, `${field.label || field.fieldName} must be at least 6 characters`)
    : z.string().min(6).optional();

export const createEmailSchema = (field: FormField) =>
  field.required
    ? z.string().email(`${field.label || field.fieldName} is invalid`)
    : z.string().email().optional();

// Dynamic schema generator combining individual schemas
export const createFormValidationSchema = (fields: FormField[]) => {
  return z.object(
    fields.reduce(
      (schema, field) => {
        switch (field.fieldType) {
          case 'text_field':
            schema[field.fieldName] = createTextFieldSchema(field);
            break;
          case 'past_date':
            schema[field.fieldName] = createPastDateSchema(field);
            break;
          case 'custom_date':
            schema[field.fieldName] = createCustomDateSchema(field);
            break;
          case 'time':
            schema[field.fieldName] = createTimeSchema(field);
            break;

          case 'password':
            schema[field.fieldName] = createPasswordSchema(field);
            break;
          case 'email':
            schema[field.fieldName] = createEmailSchema(field);
            break;
        }
        return schema;
      },
      {} as Record<string, z.ZodTypeAny>
    )
  );
};

// Real-time validation function
export const validateFormData = <T extends Record<string, any>>(
  data: T,
  schema: z.ZodObject<any>
): { isValid: boolean; errors: z.ZodError['errors'] } => {
  const result = schema.safeParse(data);

  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors,
  };
};
