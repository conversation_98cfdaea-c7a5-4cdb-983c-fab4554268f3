import { renderHook, act } from '@testing-library/react';
import useStencil from '../useStencil';
import { ui } from 'rappid';

// Mock dependencies
jest.mock('rappid', () => ({
  ui: {
    Stencil: jest.fn().mockImplementation(() => ({
      on: jest.fn(),
      off: jest.fn(),
      startDragging: jest.fn(),
      cancelDrag: jest.fn(),
    })),
  },
}));

jest.mock('../../../utils/config', () => ({
  getModuleIcon: jest.fn().mockReturnValue('test-icon.svg'),
  getModuleText: jest.fn().mockReturnValue('Test Node'),
  portsIn: { group: 'in' },
  portsOut: { group: 'out' },
}));

jest.mock('../../../joint-components/stencil', () => ({
  getStencilByType: jest.fn().mockReturnValue(function MockNode() {}),
}));

jest.mock('../../../utils/stencilUtil', () => ({
  canDrag: jest.fn().mockReturnValue(true),
  alignStencil: jest.fn(),
}));

// Mock DOM elements
Object.defineProperty(document, 'getElementsByClassName', {
  value: jest.fn().mockReturnValue([
    {
      getBoundingClientRect: () => ({
        width: 1200,
        height: 800,
      }),
    },
  ]),
});

describe('useStencil Performance Optimizations', () => {
  const mockProps = {
    paper: {} as any,
    graph: {} as any,
    isMaxNodeReached: jest.fn().mockReturnValue(false),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock requestAnimationFrame
    global.requestAnimationFrame = jest.fn((cb) => {
      cb(0);
      return 0;
    });
  });

  it('should initialize stencil with performance optimizations', () => {
    renderHook(() => useStencil(mockProps));

    expect(ui.Stencil).toHaveBeenCalledWith({
      paper: mockProps.paper,
      canDrag: expect.any(Function),
      dropAnimation: false,
      scaleClones: false,
      usePaperGrid: false,
    });
  });

  it('should pre-create cell templates for common node types', async () => {
    const { result } = renderHook(() => useStencil(mockProps));

    // Wait for the pre-warming timeout
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });

    // The createCellTemplate function should have been called for common types
    expect(result.current).toBeDefined();
  });

  it('should use requestAnimationFrame for smooth drag start', () => {
    const { result } = renderHook(() => useStencil(mockProps));

    const mockEvent = {
      nativeEvent: {},
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
      currentTarget: {
        setPointerCapture: jest.fn(),
        setAttribute: jest.fn(),
        removeAttribute: jest.fn(),
      },
      pointerId: 1,
    } as any;

    act(() => {
      result.current.startDrag(mockEvent, 'message');
    });

    expect(global.requestAnimationFrame).toHaveBeenCalled();
  });

  it('should handle drag start with pre-created templates', () => {
    const { result } = renderHook(() => useStencil(mockProps));
    const mockStencilInstance = {
      startDragging: jest.fn(),
    };

    // Mock the stencil instance
    (result.current as any).stencilInstance = mockStencilInstance;

    const mockEvent = {
      nativeEvent: {},
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
      currentTarget: {
        setPointerCapture: jest.fn(),
        setAttribute: jest.fn(),
        removeAttribute: jest.fn(),
      },
      pointerId: 1,
    } as any;

    act(() => {
      result.current.startDrag(mockEvent, 'message');
    });

    // Should use requestAnimationFrame for smooth performance
    expect(global.requestAnimationFrame).toHaveBeenCalled();
  });

  it('should pre-warm common node templates on initialization', async () => {
    const { result } = renderHook(() => useStencil(mockProps));

    // Wait for pre-warming timeout
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });

    // Verify that the hook completed initialization
    expect(result.current.stencilInstance).toBeDefined();
  });
});
