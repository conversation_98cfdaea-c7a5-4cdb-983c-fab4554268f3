// types.ts
export enum PlatformType {
  Web = 'Web',
  Mobile = 'Mobile',
  Desktop = 'Desktop',
  // Add other platforms as needed
}

export enum TimeFormat {
  TWELVE_HOUR = '12-hour',
  TWENTY_FOUR_HOUR = '24-hour',
}

export interface ChatMessage {
  sender: 'user' | 'bot';
  nodeType: 'user_message' | 'message' | 'form';
  data: any;
  conversationId?: string;
}

export interface FormField {
  fieldName: string;
  fieldType: string;
  label?: string;
  required?: boolean;
  rangeStart?: string;
  rangeEnd?: string;
}

export interface BotFormFields {
  [key: string]: string | Date | undefined;
}