import { useEffect, useMemo } from 'react';
import { FloatingField } from '@/components/ui/floating-label';
import { useAssignFlowToIntentMutation, useGetIntentItemsQuery } from '@/store/api';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useSelector } from 'react-redux';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';

export default function AppStartForm() {
  const activeFlowId = useSelector((state: any) => state.flows.activeFlowId);
  const { control, watch, setValue } = useFormContext();

  const { botId } = useBotIdParam();

  const [assignFlowToIntent] = useAssignFlowToIntentMutation();

  const { data: intentsData } = useGetIntentItemsQuery({
    filter: {
      botId: {
        eq: botId,
      },
    },
  });
  const intentItems = useMemo(
    () =>
      intentsData?.data?.items.map(item => ({
        label: item.name,
        value: item.id,
      })) || [],
    [intentsData]
  );

  const currentIntentId = watch('process.intentId');

  useEffect(() => {
    if (currentIntentId && activeFlowId) {
      assignFlowToIntent({
        intentId: currentIntentId,
        flowId: activeFlowId.id,
      });
    }
  }, [currentIntentId, activeFlowId]);

  return (
    <div className="flex-1 px-4 space-y-4 mt-4 overflow-auto">
      <FormField
        control={control}
        name="process.intentId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                as="select"
                label="Label"
                value={field.value || ''}
                onChange={field.onChange}
                options={intentItems}
                className="mt-4"
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
}
