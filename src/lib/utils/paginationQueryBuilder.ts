// lib/api/paginatedQueryBuilder.ts

import { createApi, FetchArgs } from '@reduxjs/toolkit/query/react';
import { ApiResponse, PaginatedResponse } from '@/types';

type PaginatedQueryConfig<TQueryArg, TItem> = {
  buildQuery: (args: TQueryArg) => string | FetchArgs;
  extractItems: (response: ApiResponse<PaginatedResponse<TItem>>) => TItem[];
  extractId: (item: TItem) => string | number;
};

export function buildPaginatedQuery<TItem, TQueryArg>(
  builder: ReturnType<typeof createApi>['endpoints'][string]['query'],
  config: PaginatedQueryConfig<TQueryArg, TItem>
) {
  return builder.query<ApiResponse<PaginatedResponse<TItem>>, TQueryArg>({
    query: config.buildQuery,

    serializeQueryArgs: ({ queryArgs }) => {
      return JSON.stringify(queryArgs); // unique per query param
    },

    merge: (currentCache, newCache) => {
      const existingIds = new Set(config.extractItems(currentCache).map(config.extractId));
      const newItems = config
        .extractItems(newCache)
        .filter(item => !existingIds.has(config.extractId(item)));

      currentCache.data.items.push(...newItems);
      currentCache.data.totalCount = newCache.data.totalCount;
      currentCache.data.hasMore = newCache.data.hasMore;
    },

    forceRefetch({ currentArg, previousArg }) {
      return JSON.stringify(currentArg) !== JSON.stringify(previousArg);
    },

    providesTags: result =>
      result?.data?.items
        ? [
            { type: 'Entity', id: 'LIST' },
            ...result.data.items.map(item => ({ type: 'Entity', id: config.extractId(item) })),
          ]
        : [{ type: 'Entity', id: 'LIST' }],
  });
}

export function createOptimisticCreate<TItem>(
  extractId: (item: TItem) => string | number,
  tag: string
) {
  return async (
    queryFulfilled: ReturnType<typeof entityApi.endpoints.createEntity.initiate>['then'],
    dispatch: typeof entityApi.util.updateQueryData
  ) => {
    const { data: createdItem } = await queryFulfilled;

    dispatch(
      entityApi.util.updateQueryData('getEntities', undefined, draft => {
        if (!draft.data.items.find(e => extractId(e) === extractId(createdItem.data))) {
          draft.data.items.unshift(createdItem.data);
          draft.data.totalCount += 1;
        }
      })
    );
  };
}

export function createOptimisticDelete<TItem>(extractId: (item: TItem) => string | number) {
  return (
    id: string | number,
    dispatch: typeof entityApi.util.updateQueryData,
    queryFulfilled: Promise<any>
  ) => {
    const patchResult = dispatch(
      entityApi.util.updateQueryData('getEntities', undefined, draft => {
        draft.data.items = draft.data.items.filter(item => extractId(item) !== id);
        draft.data.totalCount -= 1;
      })
    );

    queryFulfilled.catch(() => patchResult.undo());
  };
}



import {
    buildPaginatedQuery,
    createOptimisticCreate,
    createOptimisticDelete,
  } from '@/lib/api/paginatedQueryBuilder';
  
  export const entityApi = updatedApiSlice.injectEndpoints({
    endpoints: builder => ({
      getEntities: buildPaginatedQuery<Entity, EntityPaginationParams>(builder, {
        buildQuery: ({ botId, ...params }) => ({
          url: `/bots/${botId}/entities?${serializePaginationParams(params).searchParams}`,
        }),
        extractItems: (res) => res.data.items,
        extractId: (item) => item.id,
      }),
  
      createEntity: builder.mutation<ApiResponse<Entity>, CreateEntityRequest>({
        query: body => ({
          url: '/entities',
          method: 'POST',
          body,
        }),
        async onQueryStarted(arg, { dispatch, queryFulfilled }) {
          await createOptimisticCreate<Entity>(
            (item) => item.id,
            'Entity'
          )(queryFulfilled, dispatch);
        },
      }),
  
      deleteEntity: builder.mutation<void, UuidParams>({
        query: ({ id }) => ({
          url: `/entities/${id}`,
          method: 'DELETE',
        }),
        async onQueryStarted({ id }, { dispatch, queryFulfilled }) {
          createOptimisticDelete<Entity>((item) => item.id)(id, dispatch, queryFulfilled);
        },
      }),
  
      updateEntity: builder.mutation<ApiResponse<Entity>, UuidParams & UpdateEntityRequest>({
        query: ({ id, ...body }) => ({
          url: `/entities/${id}`,
          method: 'PUT',
          body,
        }),
        async onQueryStarted({ id }, { dispatch, queryFulfilled }) {
          try {
            const { data: updatedEntity } = await queryFulfilled;
  
            dispatch(
              entityApi.util.updateQueryData('getEntities', undefined, draft => {
                const index = draft.data.items.findIndex(e => e.id === id);
                if (index !== -1) {
                  draft.data.items[index] = updatedEntity.data;
                }
              })
            );
          } catch {}
        },
      }),
  
      getEntity: builder.query<ApiResponse<Entity>, UuidParams>({
        query: ({ id }) => ({ url: `/entities/${id}` }),
        providesTags: (result, error, { id }) => [{ type: 'Entity', id }],
      }),
    }),
  });
  